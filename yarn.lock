# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10

"@anduin/extends@npm:0.0.1":
  version: 0.0.1
  resolution: "@anduin/extends@npm:0.0.1"
  checksum: 10/d2014e546074ff298bb04a1b70d8e1009f793b2f506ffe69170310c153d76dc662ac1c16a774e14c29112e3478506e928058557c4b17108318b695b37decd916
  languageName: node
  linkType: hard

"@anduin/pdfium-wasm@npm:0.0.10":
  version: 0.0.10
  resolution: "@anduin/pdfium-wasm@npm:0.0.10"
  checksum: 10/b2a89abe7350fe8045648e3e3ad10059da760b17ae0e3b53177ca466fdd0be0e4937a32a16b0a2e0de06078d418cb1a2fcfaac80846aab4823a58c35dffd26d5
  languageName: node
  linkType: hard

"@anduin/pdfium@npm:0.3.3":
  version: 0.3.3
  resolution: "@anduin/pdfium@npm:0.3.3"
  dependencies:
    "@anduin/pdfium-wasm": "npm:0.0.10"
  checksum: 10/4a4d3f0f00970592acc0edc72fc3595534372837a94c45ca6e5118f9840cec95e5f36f2752f1073299991e88327e98e3b9e88f70d768f27eccf0fd1bf02ed3b3
  languageName: node
  linkType: hard

"@anduintransaction/anduin-bootstrap@workspace:out/js/anduinBootstrap/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-bootstrap@workspace:out/js/anduinBootstrap/jsSpecsDir.dest"
  dependencies:
    caniuse-lite: "npm:1.0.30001720"
    esbuild: "npm:0.25.5"
    jsdom: "npm:26.1.0"
    path-browserify: "npm:1.0.1"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-dataextract@workspace:out/modules/dataextract/dataextractApp/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-dataextract@workspace:out/modules/dataextract/dataextractApp/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    state-machine-cat: "npm:12.0.23"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-dataroom@workspace:out/modules/dataroom/dataroomApp/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-dataroom@workspace:out/modules/dataroom/dataroomApp/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-funddata@workspace:out/modules/fundData/fundDataApp/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-funddata@workspace:out/modules/fundData/fundDataApp/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    state-machine-cat: "npm:12.0.23"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-fundsub@workspace:out/modules/fundsub/fundsubApp/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-fundsub@workspace:out/modules/fundsub/fundsubApp/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    state-machine-cat: "npm:12.0.23"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-gondor@workspace:out/apps/gondor/gondorAppClient/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-gondor@workspace:out/apps/gondor/gondorAppClient/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@antv/layout": "npm:0.3.25"
    "@antv/x6": "npm:2.18.1"
    "@antv/x6-plugin-clipboard": "npm:2.1.6"
    "@antv/x6-plugin-dnd": "npm:2.1.1"
    "@antv/x6-plugin-export": "npm:2.1.6"
    "@antv/x6-plugin-history": "npm:2.2.4"
    "@antv/x6-plugin-keyboard": "npm:2.2.3"
    "@antv/x6-plugin-minimap": "npm:2.0.7"
    "@antv/x6-plugin-scroller": "npm:2.0.10"
    "@antv/x6-plugin-selection": "npm:2.2.2"
    "@antv/x6-plugin-snapline": "npm:2.1.7"
    "@antv/x6-plugin-stencil": "npm:2.1.5"
    "@antv/x6-plugin-transform": "npm:2.1.8"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    monaco-editor: "npm:0.52.2"
    monaco-vim: "npm:0.4.2"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    state-machine-cat: "npm:12.0.23"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-heimdall@workspace:out/modules/heimdall/heimdallApp/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-heimdall@workspace:out/modules/heimdall/heimdallApp/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    universal-cookie: "npm:8.0.1"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-integplatform@workspace:out/modules/integplatform/integplatformApp/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-integplatform@workspace:out/modules/integplatform/integplatformApp/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-lpprofile@workspace:out/modules/investorProfile/investorProfileApp/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-lpprofile@workspace:out/modules/investorProfile/investorProfileApp/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    state-machine-cat: "npm:12.0.23"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-maya@workspace:out/apps/maya/mayaApp/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-maya@workspace:out/apps/maya/mayaApp/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    state-machine-cat: "npm:12.0.23"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-narya@workspace:out/modules/narya/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-narya@workspace:out/modules/narya/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    state-machine-cat: "npm:12.0.23"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-olympian@workspace:out/itools/olympian/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-olympian@workspace:out/itools/olympian/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@antv/layout": "npm:0.3.25"
    "@antv/x6": "npm:2.18.1"
    "@antv/x6-plugin-clipboard": "npm:2.1.6"
    "@antv/x6-plugin-dnd": "npm:2.1.1"
    "@antv/x6-plugin-export": "npm:2.1.6"
    "@antv/x6-plugin-history": "npm:2.2.4"
    "@antv/x6-plugin-keyboard": "npm:2.2.3"
    "@antv/x6-plugin-minimap": "npm:2.0.7"
    "@antv/x6-plugin-scroller": "npm:2.0.10"
    "@antv/x6-plugin-selection": "npm:2.2.2"
    "@antv/x6-plugin-snapline": "npm:2.1.7"
    "@antv/x6-plugin-stencil": "npm:2.1.5"
    "@antv/x6-plugin-transform": "npm:2.1.8"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    monaco-editor: "npm:0.52.2"
    monaco-vim: "npm:0.4.2"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    state-machine-cat: "npm:12.0.23"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-pantheon@workspace:out/itools/pantheon/pantheon/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-pantheon@workspace:out/itools/pantheon/pantheon/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@antv/layout": "npm:0.3.25"
    "@antv/x6": "npm:2.18.1"
    "@antv/x6-plugin-clipboard": "npm:2.1.6"
    "@antv/x6-plugin-dnd": "npm:2.1.1"
    "@antv/x6-plugin-export": "npm:2.1.6"
    "@antv/x6-plugin-history": "npm:2.2.4"
    "@antv/x6-plugin-keyboard": "npm:2.2.3"
    "@antv/x6-plugin-minimap": "npm:2.0.7"
    "@antv/x6-plugin-scroller": "npm:2.0.10"
    "@antv/x6-plugin-selection": "npm:2.2.2"
    "@antv/x6-plugin-snapline": "npm:2.1.7"
    "@antv/x6-plugin-stencil": "npm:2.1.5"
    "@antv/x6-plugin-transform": "npm:2.1.8"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    monaco-editor: "npm:0.52.2"
    monaco-vim: "npm:0.4.2"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    state-machine-cat: "npm:12.0.23"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-pantheonworker@workspace:out/itools/pantheon/pantheonWebWorker/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-pantheonworker@workspace:out/itools/pantheon/pantheonWebWorker/jsSpecsDir.dest"
  dependencies:
    caniuse-lite: "npm:1.0.30001720"
    esbuild: "npm:0.25.5"
    jsdom: "npm:26.1.0"
    path-browserify: "npm:1.0.1"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-ria@workspace:out/modules/ria/riaApp/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-ria@workspace:out/modules/ria/riaApp/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    state-machine-cat: "npm:12.0.23"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/anduin-signature@workspace:out/modules/signature/signatureApp/js/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/anduin-signature@workspace:out/modules/signature/signatureApp/js/jsSpecsDir.dest"
  dependencies:
    "@anduin/extends": "npm:0.0.1"
    "@anduin/pdfium": "npm:0.3.3"
    "@floating-ui/dom": "npm:1.7.1"
    "@prismatic-io/embedded": "npm:3.1.0"
    "@sentry/browser": "npm:9.24.0"
    "@simonwep/pickr": "npm:1.9.1"
    "@types/echarts": "npm:4.9.22"
    "@types/sortablejs": "npm:1.15.8"
    "@types/tabulator-tables": "npm:6.2.6"
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-compat": "npm:2.10.0"
    "@ui5/webcomponents-fiori": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@uppy/aws-s3": "npm:4.2.3"
    "@uppy/core": "npm:4.4.5"
    "@uppy/dashboard": "npm:4.3.4"
    "@uppy/drag-drop": "npm:4.1.3"
    "@uppy/file-input": "npm:4.1.3"
    "@uppy/progress-bar": "npm:4.2.1"
    "@uppy/react": "npm:4.2.3"
    "@uppy/xhr-upload": "npm:4.3.3"
    "@viselect/vanilla": "npm:3.9.0"
    bowser: "npm:2.11.0"
    caniuse-lite: "npm:1.0.30001720"
    date-fns: "npm:4.1.0"
    dompurify: "npm:3.2.6"
    downshift: "npm:9.0.9"
    echarts: "npm:5.6.0"
    esbuild: "npm:0.25.5"
    focus-visible: "npm:5.2.1"
    fontfaceobserver: "npm:2.3.0"
    gridstack: "npm:12.2.1"
    html-react-parser: "npm:5.2.5"
    imask: "npm:7.6.1"
    jsdom: "npm:26.1.0"
    linkify-it: "npm:5.0.0"
    marked: "npm:15.0.12"
    nats.ws: "npm:1.30.3"
    numeral: "npm:2.0.6"
    papaparse: "npm:5.5.3"
    path-browserify: "npm:1.0.1"
    pdfjs-dist: "npm:5.3.31"
    pluralize-esm: "npm:9.0.5"
    quill: "npm:2.0.3"
    quill-magic-url: "npm:4.2.0"
    react: "npm:19.1.0"
    react-beautiful-dnd: "npm:13.1.1"
    react-dom: "npm:19.1.0"
    react-image-crop: "npm:11.0.10"
    react-truncate-markup: "npm:5.1.2"
    react-virtualized: "npm:9.22.6"
    sortablejs: "npm:1.15.6"
    tabulator-tables: "npm:6.3.1"
    tlds: "npm:1.259.0"
    tributejs: "npm:5.1.3"
    xlsx: "npm:0.18.5"
  languageName: unknown
  linkType: soft

"@anduintransaction/gondor-web-resources@workspace:out/gondor/gondorWebResources/jsSpecsDir.dest":
  version: 0.0.0-use.local
  resolution: "@anduintransaction/gondor-web-resources@workspace:out/gondor/gondorWebResources/jsSpecsDir.dest"
  dependencies:
    autoprefixer: "npm:10.4.21"
    caniuse-lite: "npm:1.0.30001720"
    cssnano: "npm:7.0.7"
    esbuild: "npm:0.25.5"
    jsdom: "npm:26.1.0"
    path-browserify: "npm:1.0.1"
    postcss: "npm:8.5.4"
    postcss-cli: "npm:11.0.1"
    postcss-import: "npm:16.1.0"
  languageName: unknown
  linkType: soft

"@antv/g-webgpu-core@npm:^0.7.2":
  version: 0.7.2
  resolution: "@antv/g-webgpu-core@npm:0.7.2"
  dependencies:
    eventemitter3: "npm:^4.0.0"
    gl-matrix: "npm:^3.1.0"
    lodash: "npm:^4.17.15"
    probe.gl: "npm:^3.1.1"
  checksum: 10/9d6389cbf590d4a42bb63bdec0d9e0d61ee9272913fe4c262863e248096ed5302f96419bf7bfebabf472783c0c26432eec2644e30cf753529fc2d6e3db18094b
  languageName: node
  linkType: hard

"@antv/g-webgpu-engine@npm:^0.7.2":
  version: 0.7.2
  resolution: "@antv/g-webgpu-engine@npm:0.7.2"
  dependencies:
    "@antv/g-webgpu-core": "npm:^0.7.2"
    gl-matrix: "npm:^3.1.0"
    lodash: "npm:^4.17.15"
    regl: "npm:^1.3.11"
  checksum: 10/6a8b68f500e7b0ba6564084b87f1b49a3198feb530122e13ebee7329ce3c0b30ad3a3d390234dfea5411070810862c9d20c0e54c48d3d4affdbaedd1fc7b493a
  languageName: node
  linkType: hard

"@antv/g-webgpu@npm:0.7.2":
  version: 0.7.2
  resolution: "@antv/g-webgpu@npm:0.7.2"
  dependencies:
    "@antv/g-webgpu-core": "npm:^0.7.2"
    "@antv/g-webgpu-engine": "npm:^0.7.2"
    gl-matrix: "npm:^3.1.0"
    gl-vec2: "npm:^1.3.0"
    lodash: "npm:^4.17.15"
  checksum: 10/4bb31ef684873a91dffb8f20733c26f1e06a87aecff265d67829370fc16589d0a76126a16b8d278176d44cffcbd263617b2b4aef367931e386a4a97baed5f378
  languageName: node
  linkType: hard

"@antv/graphlib@npm:^1.0.0":
  version: 1.2.0
  resolution: "@antv/graphlib@npm:1.2.0"
  checksum: 10/63b6eade2a8a5ebed3192ab4cbeea5815209ee4ecabfc4b6cd76522f73af9139e339c0afd7bf0f365817badfc042614927339bfc22fc7049ebf200cdcc01eb1d
  languageName: node
  linkType: hard

"@antv/layout@npm:0.3.25":
  version: 0.3.25
  resolution: "@antv/layout@npm:0.3.25"
  dependencies:
    "@antv/g-webgpu": "npm:0.7.2"
    "@antv/graphlib": "npm:^1.0.0"
    "@antv/util": "npm:^3.3.2"
    d3-force: "npm:^2.1.1"
    d3-quadtree: "npm:^2.0.0"
    dagre-compound: "npm:^0.0.11"
    ml-matrix: "npm:6.5.0"
  checksum: 10/fb47b0cc86c63d39b0da231694b83503a2a619c0e22d0cd0c04dcc31f8b2f5bd09be4147d639809f1aab6e3761b59f33bb9070bf3c1a21cb4ecc716fdce6c165
  languageName: node
  linkType: hard

"@antv/util@npm:^3.3.2":
  version: 3.3.10
  resolution: "@antv/util@npm:3.3.10"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    gl-matrix: "npm:^3.3.0"
    tslib: "npm:^2.3.1"
  checksum: 10/9272aa4c53eaaf3ba9a2e5c6747be20ac1f3730225e2a9e8f7fb0a847731472619301bfe4f6944c8e36acbf82e33a9ac00043e7d29fc4ed34343486dad734acb
  languageName: node
  linkType: hard

"@antv/x6-common@npm:^2.0.16":
  version: 2.0.17
  resolution: "@antv/x6-common@npm:2.0.17"
  dependencies:
    lodash-es: "npm:^4.17.15"
    utility-types: "npm:^3.10.0"
  checksum: 10/f15aae81477a365ae89a8f4a53fdc4e87aa9637c97cacad2a4ed562b5015be66ddf956805644a1dad561a08a3f029d914268aebda9bea2adb74b9a5b16c79528
  languageName: node
  linkType: hard

"@antv/x6-geometry@npm:^2.0.5":
  version: 2.0.5
  resolution: "@antv/x6-geometry@npm:2.0.5"
  checksum: 10/b9adc61404664325b5e52e6dd1d91213b6daf6042431f89cf4d39c9c13842e7a86dcfcfff90e2b2a87c1d092fd6d8cf89228462fd3f6118b580843f99d0b055f
  languageName: node
  linkType: hard

"@antv/x6-plugin-clipboard@npm:2.1.6":
  version: 2.1.6
  resolution: "@antv/x6-plugin-clipboard@npm:2.1.6"
  peerDependencies:
    "@antv/x6": ^2.x
  checksum: 10/1313abe31a2e526bf9faffc9cfff1c3a54c491a8bd170677e40c31a8e2bee0d8cc9d264305793e43be2df552022d4654040b4be30fbb218ce21f49c0800bca13
  languageName: node
  linkType: hard

"@antv/x6-plugin-dnd@npm:2.1.1, @antv/x6-plugin-dnd@npm:^2.x":
  version: 2.1.1
  resolution: "@antv/x6-plugin-dnd@npm:2.1.1"
  peerDependencies:
    "@antv/x6": ^2.x
  checksum: 10/6515383a102fe6bdb34a48e2ef3c21b69a0662b661fb43bddab56b924a2e58c2b6d1bf227a43ffa630dc88fc8ca5c7e5976ddeb96d3f15c7e73cfd468ca46447
  languageName: node
  linkType: hard

"@antv/x6-plugin-export@npm:2.1.6":
  version: 2.1.6
  resolution: "@antv/x6-plugin-export@npm:2.1.6"
  peerDependencies:
    "@antv/x6": ^2.x
  checksum: 10/a392e087d4a5655068f343438e1a50d03fd134cc87088d3d6ad9e296b177cf8406e7b938a693fce3f196c087cb451db5f6f949b4a376bc9fa75594dc1a5ed0ec
  languageName: node
  linkType: hard

"@antv/x6-plugin-history@npm:2.2.4":
  version: 2.2.4
  resolution: "@antv/x6-plugin-history@npm:2.2.4"
  peerDependencies:
    "@antv/x6": ^2.x
  checksum: 10/5c090132e3160fa205878fa6b3bd2d482975253dbd85b0ae71975cee80eef55805cee96a68284e39e4658c14fd9ac7a83a067091cb85bac1fec1f97cf2e0abfc
  languageName: node
  linkType: hard

"@antv/x6-plugin-keyboard@npm:2.2.3":
  version: 2.2.3
  resolution: "@antv/x6-plugin-keyboard@npm:2.2.3"
  dependencies:
    mousetrap: "npm:^1.6.5"
  peerDependencies:
    "@antv/x6": ^2.x
  checksum: 10/ed18403c7bf905c15624b90629eab94b1eadc5f05b8e2e8c18c4214bba850c35f9edd486cfc2c3bc93d20d69669f09001c77b9016e265aaad38818067951acce
  languageName: node
  linkType: hard

"@antv/x6-plugin-minimap@npm:2.0.7":
  version: 2.0.7
  resolution: "@antv/x6-plugin-minimap@npm:2.0.7"
  peerDependencies:
    "@antv/x6": ^2.x
  checksum: 10/8e093c853069b34fd1ec12eb2ff21359ccec490d216194075ae319ecd94814c5facc1f42aa841a17989b79ecc1ca8c581c553665dd64f702c2b21e53294c0f0d
  languageName: node
  linkType: hard

"@antv/x6-plugin-scroller@npm:2.0.10":
  version: 2.0.10
  resolution: "@antv/x6-plugin-scroller@npm:2.0.10"
  peerDependencies:
    "@antv/x6": ^2.x
  checksum: 10/4d12465839e662c50f5f8e7171c8e83acc10d90284a07d0de6508444de60f7657fb11f2f45c6def024b692f1c289eded101bbe7e4e2c4023f0780055c3fcdbc0
  languageName: node
  linkType: hard

"@antv/x6-plugin-selection@npm:2.2.2":
  version: 2.2.2
  resolution: "@antv/x6-plugin-selection@npm:2.2.2"
  peerDependencies:
    "@antv/x6": ^2.x
  checksum: 10/2abdf3eb4d2c343e34e17f13c906ebe307e9826169124158810a083c8166d9d79bb44605d25f5d777ce904fe23c101289f89031a7d7a7391f544d39f0f5e2621
  languageName: node
  linkType: hard

"@antv/x6-plugin-snapline@npm:2.1.7":
  version: 2.1.7
  resolution: "@antv/x6-plugin-snapline@npm:2.1.7"
  peerDependencies:
    "@antv/x6": ^2.x
  checksum: 10/fec145828592cb49d9a8c001131747fd66bb41ce30f98fbf96b13dad3f7e50b8bb92a586451bb6c3fa3fad97f73464074be64634f8c4ab800e046cb80d348a05
  languageName: node
  linkType: hard

"@antv/x6-plugin-stencil@npm:2.1.5":
  version: 2.1.5
  resolution: "@antv/x6-plugin-stencil@npm:2.1.5"
  dependencies:
    "@antv/x6-plugin-dnd": "npm:^2.x"
  peerDependencies:
    "@antv/x6": ^2.x
  checksum: 10/6b5b0b51bf5491c00fe3354487f668a1cd32a20dec8cec5a79fb41dfaaac0a9b75e68efbdeb7f72d5773e62fdee7af06d30657b931f2508e6240eabcbff3357a
  languageName: node
  linkType: hard

"@antv/x6-plugin-transform@npm:2.1.8":
  version: 2.1.8
  resolution: "@antv/x6-plugin-transform@npm:2.1.8"
  peerDependencies:
    "@antv/x6": ^2.x
  checksum: 10/630a06192f16a51914ef5358d7152d446dc8d032abbbba5f5922005d49b7fff1bf5d272f38dae691cf6617c2bb34a0af6ddd93718e14e404f4a05513b7913e12
  languageName: node
  linkType: hard

"@antv/x6@npm:2.18.1":
  version: 2.18.1
  resolution: "@antv/x6@npm:2.18.1"
  dependencies:
    "@antv/x6-common": "npm:^2.0.16"
    "@antv/x6-geometry": "npm:^2.0.5"
    utility-types: "npm:^3.10.0"
  checksum: 10/1b8bcf8074296198fcece1a2a09302764805fa837d572aa5f6dd86cfc4a52bbd0e49a9f4b7f49a7c7fa84ac900794c7d7da0ab4f59fcae27252513a435067fef
  languageName: node
  linkType: hard

"@asamuzakjp/css-color@npm:^2.8.2":
  version: 2.8.3
  resolution: "@asamuzakjp/css-color@npm:2.8.3"
  dependencies:
    "@csstools/css-calc": "npm:^2.1.1"
    "@csstools/css-color-parser": "npm:^3.0.7"
    "@csstools/css-parser-algorithms": "npm:^3.0.4"
    "@csstools/css-tokenizer": "npm:^3.0.3"
    lru-cache: "npm:^10.4.3"
  checksum: 10/3fbd6b975cfca220a0620843776e7d266b880293a9e3364a48de11ca3eb54af8209343d01842a7c98d2737e457294a7621a5f6671aaf5f12e1634d10808f2508
  languageName: node
  linkType: hard

"@babel/runtime-corejs3@npm:^7.24.4":
  version: 7.26.0
  resolution: "@babel/runtime-corejs3@npm:7.26.0"
  dependencies:
    core-js-pure: "npm:^3.30.2"
    regenerator-runtime: "npm:^0.14.0"
  checksum: 10/fd813d8b5bfc412c083033638c937e13f621b3223161c4a20bb8532d77ae622b620915476bd265670f6a8fc1a76a017ffd738ad25ad24431953e3725247c6520
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.0.0, @babel/runtime@npm:^7.15.4, @babel/runtime@npm:^7.24.5, @babel/runtime@npm:^7.7.2, @babel/runtime@npm:^7.8.7, @babel/runtime@npm:^7.9.2":
  version: 7.26.0
  resolution: "@babel/runtime@npm:7.26.0"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: 10/9f4ea1c1d566c497c052d505587554e782e021e6ccd302c2ad7ae8291c8e16e3f19d4a7726fb64469e057779ea2081c28b7dbefec6d813a22f08a35712c0f699
  languageName: node
  linkType: hard

"@csstools/color-helpers@npm:^5.0.1":
  version: 5.0.1
  resolution: "@csstools/color-helpers@npm:5.0.1"
  checksum: 10/4cb25b34997c9b0e9f401833e27942636494bc3c7fda5c6633026bc3fdfdda1c67be68ea048058bfba449a86ec22332e23b4ec5982452c50b67880c4cb13a660
  languageName: node
  linkType: hard

"@csstools/css-calc@npm:^2.1.1":
  version: 2.1.1
  resolution: "@csstools/css-calc@npm:2.1.1"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^3.0.4
    "@csstools/css-tokenizer": ^3.0.3
  checksum: 10/60e8808c261eeebb15517c0f368672494095bb10e90177dfc492f956fc432760d84b17dc19db739a2e23cac0013f4bcf37bb93947f9741b95b7227eeaced250b
  languageName: node
  linkType: hard

"@csstools/css-color-parser@npm:^3.0.7":
  version: 3.0.7
  resolution: "@csstools/css-color-parser@npm:3.0.7"
  dependencies:
    "@csstools/color-helpers": "npm:^5.0.1"
    "@csstools/css-calc": "npm:^2.1.1"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^3.0.4
    "@csstools/css-tokenizer": ^3.0.3
  checksum: 10/efceb60608f3fc2b6da44d5be7720a8b302e784f05c1c12f17a1da4b4b9893b2e20d0ea74ac2c2d6d5ca9b64ee046d05f803c7b78581fd5a3f85e78acfc5d98e
  languageName: node
  linkType: hard

"@csstools/css-parser-algorithms@npm:^3.0.4":
  version: 3.0.4
  resolution: "@csstools/css-parser-algorithms@npm:3.0.4"
  peerDependencies:
    "@csstools/css-tokenizer": ^3.0.3
  checksum: 10/dfb6926218d9f8ba25d8b43ea46c03863c819481f8c55e4de4925780eaab9e6bcd6bead1d56b4ef82d09fcd9d69a7db2750fa9db08eece9470fd499dc76d0edb
  languageName: node
  linkType: hard

"@csstools/css-tokenizer@npm:^3.0.3":
  version: 3.0.3
  resolution: "@csstools/css-tokenizer@npm:3.0.3"
  checksum: 10/6baa3160e426e1f177b8f10d54ec7a4a596090f65a05f16d7e9e4da049962a404eabc5f885f4867093702c259cd4080ac92a438326e22dea015201b3e71f5bbb
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/aix-ppc64@npm:0.25.5"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/android-arm64@npm:0.25.5"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/android-arm@npm:0.25.5"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/android-x64@npm:0.25.5"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/darwin-arm64@npm:0.25.5"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/darwin-x64@npm:0.25.5"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/freebsd-arm64@npm:0.25.5"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/freebsd-x64@npm:0.25.5"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-arm64@npm:0.25.5"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-arm@npm:0.25.5"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-ia32@npm:0.25.5"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-loong64@npm:0.25.5"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-mips64el@npm:0.25.5"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-ppc64@npm:0.25.5"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-riscv64@npm:0.25.5"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-s390x@npm:0.25.5"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-x64@npm:0.25.5"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/netbsd-arm64@npm:0.25.5"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/netbsd-x64@npm:0.25.5"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/openbsd-arm64@npm:0.25.5"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/openbsd-x64@npm:0.25.5"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/sunos-x64@npm:0.25.5"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/win32-arm64@npm:0.25.5"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/win32-ia32@npm:0.25.5"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/win32-x64@npm:0.25.5"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.7.1":
  version: 1.7.1
  resolution: "@floating-ui/core@npm:1.7.1"
  dependencies:
    "@floating-ui/utils": "npm:^0.2.9"
  checksum: 10/5dbe5d92dcdaef6a915a6bfaa432a684b0a021e6eca0eab796216eecb0870282f8b9ecfcf449f1cac94cc24d8c5114d1677b1f7a6e11e2642967065f2497ce26
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:1.7.1":
  version: 1.7.1
  resolution: "@floating-ui/dom@npm:1.7.1"
  dependencies:
    "@floating-ui/core": "npm:^1.7.1"
    "@floating-ui/utils": "npm:^0.2.9"
  checksum: 10/77f385e0202855aaeee7c8c96e40c8cd06c63f1946ed666824beed40b98e9414a5a8c19ac8c8f68653577eceb1866261a785d3d9855a531bd85d2865024ca9e9
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.2.9":
  version: 0.2.9
  resolution: "@floating-ui/utils@npm:0.2.9"
  checksum: 10/0ca786347db3dd8d9034b86d1449fabb96642788e5900cc5f2aee433cd7b243efbcd7a165bead50b004ee3f20a90ddebb6a35296fc41d43cfd361b6f01b69ffb
  languageName: node
  linkType: hard

"@hpcc-js/wasm-graphviz@npm:1.7.0":
  version: 1.7.0
  resolution: "@hpcc-js/wasm-graphviz@npm:1.7.0"
  checksum: 10/62dd28306706f1459b5a1442cdb80669a0641d6a17cee5f65ae25f4354fac5bb3e0692cfe181b8321aa6a890790bb438da6d602ceb79ed836e51039a47312bb4
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10/e9ed5fd27c3aec1095e3a16e0c0cf148d1fee55a38665c35f7b3f86a9b5d00d042ddaabc98e8a1cb7463b9378c15f22a94eb35e99469c201453eb8375191f243
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10/4412e9e6713c89c1e66d80bb0bb5a2a93192f10477623a27d08f228ba0316bb880affabc5bfe7f838f58a34d26c2c190da726e576cdfc18c49a72e89adabdcf5
  languageName: node
  linkType: hard

"@lit-labs/ssr-dom-shim@npm:^1.1.2":
  version: 1.3.0
  resolution: "@lit-labs/ssr-dom-shim@npm:1.3.0"
  checksum: 10/a15c5d145a20f367a392cff91f2091ffe54457119ac26569670bbbe32760f86d1e250f865dc1bd0604641106376776c4862a8fff9adb44f9881b510747c08680
  languageName: node
  linkType: hard

"@napi-rs/canvas-android-arm64@npm:0.1.68":
  version: 0.1.68
  resolution: "@napi-rs/canvas-android-arm64@npm:0.1.68"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@napi-rs/canvas-darwin-arm64@npm:0.1.68":
  version: 0.1.68
  resolution: "@napi-rs/canvas-darwin-arm64@npm:0.1.68"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@napi-rs/canvas-darwin-x64@npm:0.1.68":
  version: 0.1.68
  resolution: "@napi-rs/canvas-darwin-x64@npm:0.1.68"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@napi-rs/canvas-linux-arm-gnueabihf@npm:0.1.68":
  version: 0.1.68
  resolution: "@napi-rs/canvas-linux-arm-gnueabihf@npm:0.1.68"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@napi-rs/canvas-linux-arm64-gnu@npm:0.1.68":
  version: 0.1.68
  resolution: "@napi-rs/canvas-linux-arm64-gnu@npm:0.1.68"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/canvas-linux-arm64-musl@npm:0.1.68":
  version: 0.1.68
  resolution: "@napi-rs/canvas-linux-arm64-musl@npm:0.1.68"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@napi-rs/canvas-linux-riscv64-gnu@npm:0.1.68":
  version: 0.1.68
  resolution: "@napi-rs/canvas-linux-riscv64-gnu@npm:0.1.68"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/canvas-linux-x64-gnu@npm:0.1.68":
  version: 0.1.68
  resolution: "@napi-rs/canvas-linux-x64-gnu@npm:0.1.68"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/canvas-linux-x64-musl@npm:0.1.68":
  version: 0.1.68
  resolution: "@napi-rs/canvas-linux-x64-musl@npm:0.1.68"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@napi-rs/canvas-win32-x64-msvc@npm:0.1.68":
  version: 0.1.68
  resolution: "@napi-rs/canvas-win32-x64-msvc@npm:0.1.68"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@napi-rs/canvas@npm:^0.1.67":
  version: 0.1.68
  resolution: "@napi-rs/canvas@npm:0.1.68"
  dependencies:
    "@napi-rs/canvas-android-arm64": "npm:0.1.68"
    "@napi-rs/canvas-darwin-arm64": "npm:0.1.68"
    "@napi-rs/canvas-darwin-x64": "npm:0.1.68"
    "@napi-rs/canvas-linux-arm-gnueabihf": "npm:0.1.68"
    "@napi-rs/canvas-linux-arm64-gnu": "npm:0.1.68"
    "@napi-rs/canvas-linux-arm64-musl": "npm:0.1.68"
    "@napi-rs/canvas-linux-riscv64-gnu": "npm:0.1.68"
    "@napi-rs/canvas-linux-x64-gnu": "npm:0.1.68"
    "@napi-rs/canvas-linux-x64-musl": "npm:0.1.68"
    "@napi-rs/canvas-win32-x64-msvc": "npm:0.1.68"
  dependenciesMeta:
    "@napi-rs/canvas-android-arm64":
      optional: true
    "@napi-rs/canvas-darwin-arm64":
      optional: true
    "@napi-rs/canvas-darwin-x64":
      optional: true
    "@napi-rs/canvas-linux-arm-gnueabihf":
      optional: true
    "@napi-rs/canvas-linux-arm64-gnu":
      optional: true
    "@napi-rs/canvas-linux-arm64-musl":
      optional: true
    "@napi-rs/canvas-linux-riscv64-gnu":
      optional: true
    "@napi-rs/canvas-linux-x64-gnu":
      optional: true
    "@napi-rs/canvas-linux-x64-musl":
      optional: true
    "@napi-rs/canvas-win32-x64-msvc":
      optional: true
  checksum: 10/b03fdf0ba679ca6a2f89894984f7f80fbc42a4e8070a834799c47120ffdb2625b00859b719985149393560d6be4412d7f8a7ffe697542aceda30b6982b24f612
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10/775c9a7eb1f88c195dfb3bce70c31d0fe2a12b28b754e25c08a3edb4bc4816bfedb7ac64ef1e730579d078ca19dacf11630e99f8f3c3e0fd7b23caa5fd6d30a6
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10/405c4490e1ff11cf299775449a3c254a366a4b1ffc79d87159b0ee7d5558ac9f6a2f8c0735fd6ff3873cef014cb1a44a5f9127cb6a1b2dbc408718cca9365b5a
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10/115e8ceeec6bc69dff2048b35c0ab4f8bbee12d8bb6c1f4af758604586d802b6e669dcb02dda61d078de42c2b4ddce41b3d9e726d7daa6b4b850f4adbf7333ff
  languageName: node
  linkType: hard

"@prismatic-io/embedded@npm:3.1.0":
  version: 3.1.0
  resolution: "@prismatic-io/embedded@npm:3.1.0"
  dependencies:
    "@prismatic-io/translations": "npm:^2.1.1"
    lodash.merge: "npm:4.6.2"
    url-join: "npm:5.0.0"
  checksum: 10/2818d0df82e6905821c1b2809b024124f302e40506561329b337678980119741e7fee86ab8843af1fe79dcaf053631035dd2f5d86db173eea1b75745686236b3
  languageName: node
  linkType: hard

"@prismatic-io/translations@npm:^2.1.1":
  version: 2.1.3
  resolution: "@prismatic-io/translations@npm:2.1.3"
  checksum: 10/db0da536cf6d67afa530157feed56537db5c4e24fd94eb9424b421b48b032393ced287e7646e6304710d27cdf2fb6bce1bf168bd468177d9da07ccd5826dd9f5
  languageName: node
  linkType: hard

"@probe.gl/env@npm:3.6.0":
  version: 3.6.0
  resolution: "@probe.gl/env@npm:3.6.0"
  dependencies:
    "@babel/runtime": "npm:^7.0.0"
  checksum: 10/7bdf889a12265387fb33f226eb53dc8250dad0ceaa55dfdcf965013c5c8bf0c0c439ccf43abd45c1260130232271d51858cbef5465321f576dd39dd2b755eb9b
  languageName: node
  linkType: hard

"@probe.gl/log@npm:3.6.0":
  version: 3.6.0
  resolution: "@probe.gl/log@npm:3.6.0"
  dependencies:
    "@babel/runtime": "npm:^7.0.0"
    "@probe.gl/env": "npm:3.6.0"
  checksum: 10/2f0603fa00b3fff66886faf8ce3b0698e754a2a988e6fe3fb8e63c033dfb3a1afd63d5c9b8298d82dea66a3e9cba6667a4a7852b4c2e2c069ca720650959fc47
  languageName: node
  linkType: hard

"@probe.gl/stats@npm:3.6.0":
  version: 3.6.0
  resolution: "@probe.gl/stats@npm:3.6.0"
  dependencies:
    "@babel/runtime": "npm:^7.0.0"
  checksum: 10/c54a32a9db3f47fa7e6e8f78589a34bd33e3f91f4d73030b4b07fe22facc31aa4eceaa586942e12927aa0c11b7a85db8a16c39f6da561801128da46770fdcd99
  languageName: node
  linkType: hard

"@sap-theming/theming-base-content@npm:11.26.0":
  version: 11.26.0
  resolution: "@sap-theming/theming-base-content@npm:11.26.0"
  checksum: 10/963db2058c149cbe4e280e2e52a53aac24e3eebc95dea8dd0fb41f02b652777e288098f31f506e764840e96ba8413bf5a2334f91f7ec65d12ad3a8e1fbb32ab8
  languageName: node
  linkType: hard

"@sentry-internal/browser-utils@npm:9.24.0":
  version: 9.24.0
  resolution: "@sentry-internal/browser-utils@npm:9.24.0"
  dependencies:
    "@sentry/core": "npm:9.24.0"
  checksum: 10/7a11752a990d878105ff1040c461a2113aa1a4df5922b0111b5f8788e4661367e3e3b3a1e51186ff66380e635e3fca854b65ee75a8a5b67cb35ede034a41c96d
  languageName: node
  linkType: hard

"@sentry-internal/feedback@npm:9.24.0":
  version: 9.24.0
  resolution: "@sentry-internal/feedback@npm:9.24.0"
  dependencies:
    "@sentry/core": "npm:9.24.0"
  checksum: 10/0cfaf7d20d9657f72084872e552f46e61dc9f24fa1a3adaa6d121a236366176c3e7427ed77dcf6056ac7db9d54ea774b36603254b11c0ffc4fc44bfee7a232b4
  languageName: node
  linkType: hard

"@sentry-internal/replay-canvas@npm:9.24.0":
  version: 9.24.0
  resolution: "@sentry-internal/replay-canvas@npm:9.24.0"
  dependencies:
    "@sentry-internal/replay": "npm:9.24.0"
    "@sentry/core": "npm:9.24.0"
  checksum: 10/f35dd4929bf0ed82116bd2c8a46170fc5323dc51eefad76476e1a4ace3b19efc2fefec5d4d30a3da798ced14faee5c835bad95bc9b1d33c8ae8bcdbed3adbe87
  languageName: node
  linkType: hard

"@sentry-internal/replay@npm:9.24.0":
  version: 9.24.0
  resolution: "@sentry-internal/replay@npm:9.24.0"
  dependencies:
    "@sentry-internal/browser-utils": "npm:9.24.0"
    "@sentry/core": "npm:9.24.0"
  checksum: 10/43d069f75139b8d0478a5295ec14241f0ee4a20bb5efded43f864566b186d923b5218a3867cf85cbec94106716d185eca349827a820bc861723422d5450619b7
  languageName: node
  linkType: hard

"@sentry/browser@npm:9.24.0":
  version: 9.24.0
  resolution: "@sentry/browser@npm:9.24.0"
  dependencies:
    "@sentry-internal/browser-utils": "npm:9.24.0"
    "@sentry-internal/feedback": "npm:9.24.0"
    "@sentry-internal/replay": "npm:9.24.0"
    "@sentry-internal/replay-canvas": "npm:9.24.0"
    "@sentry/core": "npm:9.24.0"
  checksum: 10/34edbc25665f4691bd20a84ecd5ebe24d5770837c050a6635506fc8e43bde7584fc4b743221beb90a7b3a78e01ebac485e2beb4a53d579f82a9d67fa6ebcd183
  languageName: node
  linkType: hard

"@sentry/core@npm:9.24.0":
  version: 9.24.0
  resolution: "@sentry/core@npm:9.24.0"
  checksum: 10/d796b9099f22484d3c374d382c9e9f2743f0300f4e7650c410e7888954d5b7b0f1b13218d2eb4e8d829143678f5d92509d2bf78e308519e7e1db8e745f7c5c75
  languageName: node
  linkType: hard

"@simonwep/pickr@npm:1.9.1":
  version: 1.9.1
  resolution: "@simonwep/pickr@npm:1.9.1"
  dependencies:
    core-js: "npm:3.37.0"
    nanopop: "npm:2.4.2"
  checksum: 10/eeff3b8af2803b5e5fb7d1b435252ca8137d707e2076a3e08020db555b7b1d8c9bc733d0098407e09f7bcb70f0825af3ad2a0bf4e81033cc565db1f594244709
  languageName: node
  linkType: hard

"@transloadit/prettier-bytes@npm:^0.3.4":
  version: 0.3.5
  resolution: "@transloadit/prettier-bytes@npm:0.3.5"
  checksum: 10/467162f0044c4f9e9c03e01d4f6e7a34ed27809c8e1a3af2b19091e7b50d2637c7c6c2666274f8ee4bd4776672f8e5134d0c666af92346b11bf9f2e2db81f3b5
  languageName: node
  linkType: hard

"@trysound/sax@npm:0.2.0":
  version: 0.2.0
  resolution: "@trysound/sax@npm:0.2.0"
  checksum: 10/7379713eca480ac0d9b6c7b063e06b00a7eac57092354556c81027066eb65b61ea141a69d0cc2e15d32e05b2834d4c9c2184793a5e36bbf5daf05ee5676af18c
  languageName: node
  linkType: hard

"@types/echarts@npm:4.9.22":
  version: 4.9.22
  resolution: "@types/echarts@npm:4.9.22"
  dependencies:
    "@types/zrender": "npm:*"
  checksum: 10/abc05f45b29afb019c33c2e5c96e553a43ca9bfa13b188d10e097a9005469aff10149c4ab7f8b8abeabdb95035817966446a97b78362a8c7f28fc910c080d135
  languageName: node
  linkType: hard

"@types/hoist-non-react-statics@npm:^3.3.0":
  version: 3.3.6
  resolution: "@types/hoist-non-react-statics@npm:3.3.6"
  dependencies:
    "@types/react": "npm:*"
    hoist-non-react-statics: "npm:^3.3.0"
  checksum: 10/f03e43bd081876c49584ffa0eb690d69991f258203efca44dcc30efdda49a50653ff06402917d1edc9cb7e2adebbe9e2d1d0e739bc99c1b5372103b1cc534e47
  languageName: node
  linkType: hard

"@types/jquery@npm:~3.5.13":
  version: 3.5.32
  resolution: "@types/jquery@npm:3.5.32"
  dependencies:
    "@types/sizzle": "npm:*"
  checksum: 10/2c67cac338828870ead5c5e608f5fa5ab8101598ed4572cf49b58c342adffe8918d2e2fc94d7954e6b98a889cef8c3f4e6f44b8fecb75e80854b0f9cf9dd18a1
  languageName: node
  linkType: hard

"@types/openui5@npm:^1.113.0":
  version: 1.131.0
  resolution: "@types/openui5@npm:1.131.0"
  dependencies:
    "@types/jquery": "npm:~3.5.13"
    "@types/qunit": "npm:^2.5.4"
  checksum: 10/219369024d608566bd696bb3ebe9f25ede206542a69a7c107f6f24d346e32a2d6b721386cbbb7f39f5a112f667480730f980dd46c197ac7f65f2e16eedd45e13
  languageName: node
  linkType: hard

"@types/quill@npm:^2.0.9":
  version: 2.0.14
  resolution: "@types/quill@npm:2.0.14"
  dependencies:
    parchment: "npm:^1.1.2"
    quill-delta: "npm:^5.1.0"
  checksum: 10/ef9a5f2bc9f2ce0909bfbfe841855430eaebd396afea515fe0ed41962f3cf4ad125221b8bfc2b3bb69d8bcede876e43cdc262f9a42637270342237085574c495
  languageName: node
  linkType: hard

"@types/qunit@npm:^2.5.4":
  version: 2.19.12
  resolution: "@types/qunit@npm:2.19.12"
  checksum: 10/6d509f7e8fe5eedf29643a88f4158b5465238a09fa6ea6541564a9fe5cf06148ab9ed3f825dc23a59162fde54f7eca10663ba21d8a4355dddd9b241782e72273
  languageName: node
  linkType: hard

"@types/react-redux@npm:^7.1.20":
  version: 7.1.34
  resolution: "@types/react-redux@npm:7.1.34"
  dependencies:
    "@types/hoist-non-react-statics": "npm:^3.3.0"
    "@types/react": "npm:*"
    hoist-non-react-statics: "npm:^3.3.0"
    redux: "npm:^4.0.0"
  checksum: 10/febcd1db0c83c5002c6bee0fdda9e70da0653454ffbb72d6c37cbf2f5c005e06fb5271cff344d7164c385c944526565282de9a95ff379e040476b71d27fc2512
  languageName: node
  linkType: hard

"@types/react@npm:*":
  version: 19.0.7
  resolution: "@types/react@npm:19.0.7"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10/09522132bde715888e57c61fc89a87db06699872535450296f1c975c5014a7953ff08c3bff6b37d4a4515ceeb65b09cc5f841513cc8fede65fec788790b21154
  languageName: node
  linkType: hard

"@types/retry@npm:0.12.2":
  version: 0.12.2
  resolution: "@types/retry@npm:0.12.2"
  checksum: 10/e5675035717b39ce4f42f339657cae9637cf0c0051cf54314a6a2c44d38d91f6544be9ddc0280587789b6afd056be5d99dbe3e9f4df68c286c36321579b1bf4a
  languageName: node
  linkType: hard

"@types/sizzle@npm:*":
  version: 2.3.9
  resolution: "@types/sizzle@npm:2.3.9"
  checksum: 10/413811a79e7e9f1d8f47e6047ae0aea1530449d612304cdda1c30018e3d053b8544861ec2c70bdeca75a0a010192e6bb78efc6fb4caaafdd65c4eee90066686a
  languageName: node
  linkType: hard

"@types/sortablejs@npm:1.15.8":
  version: 1.15.8
  resolution: "@types/sortablejs@npm:1.15.8"
  checksum: 10/aea58b08cf45f5e9633707a8df0df1212595c731bbdfd29805487138fdd0d8c51fa5c741999738a645c1e801d43a92ba0d3fb5b45625b52e247c56588aef6c55
  languageName: node
  linkType: hard

"@types/tabulator-tables@npm:6.2.6":
  version: 6.2.6
  resolution: "@types/tabulator-tables@npm:6.2.6"
  checksum: 10/1a95550bd6b6d33ddc7409a43c55db92f4fc9c88b9b921836b68008debe0e18b98dad9dabbb8c4916c50b66c15401dfd8e37abca6a4ec77727b4813437280299
  languageName: node
  linkType: hard

"@types/trusted-types@npm:^2.0.2, @types/trusted-types@npm:^2.0.7":
  version: 2.0.7
  resolution: "@types/trusted-types@npm:2.0.7"
  checksum: 10/8e4202766a65877efcf5d5a41b7dd458480b36195e580a3b1085ad21e948bc417d55d6f8af1fd2a7ad008015d4117d5fdfe432731157da3c68678487174e4ba3
  languageName: node
  linkType: hard

"@types/zrender@npm:*":
  version: 4.0.6
  resolution: "@types/zrender@npm:4.0.6"
  checksum: 10/07115466bf90d3f4fa86ad47bc5013b7c68371d2cb37e9411af0d8c4511025b56163d29efc36c19b9918e4672ee63c8a175eb50d299d9bcab193b879517d7ac7
  languageName: node
  linkType: hard

"@ui5/webcomponents-base@npm:2.10.0":
  version: 2.10.0
  resolution: "@ui5/webcomponents-base@npm:2.10.0"
  dependencies:
    "@lit-labs/ssr-dom-shim": "npm:^1.1.2"
    lit-html: "npm:^2.0.1"
  checksum: 10/e503c10429d5d86c989c480b548d4e8e91fc602a38b331a65eaea50cb1640a67821bdb07e1641b39b207931af6e819da5013de7d9370b5be976b0e35a0160d49
  languageName: node
  linkType: hard

"@ui5/webcomponents-compat@npm:2.10.0":
  version: 2.10.0
  resolution: "@ui5/webcomponents-compat@npm:2.10.0"
  dependencies:
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@ui5/webcomponents-theming": "npm:2.10.0"
  checksum: 10/898889e5046018f24f2533d4f63ea905c5b797a40e4998936056736ebc579173263cd88f7ec2cec24947d66430f87f233f858c000772fb650327edcc2a28f480
  languageName: node
  linkType: hard

"@ui5/webcomponents-fiori@npm:2.10.0":
  version: 2.10.0
  resolution: "@ui5/webcomponents-fiori@npm:2.10.0"
  dependencies:
    "@ui5/webcomponents": "npm:2.10.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@ui5/webcomponents-theming": "npm:2.10.0"
    "@zxing/library": "npm:^0.21.3"
  checksum: 10/afeb6ca6646dfd66d13af0a59b6a877b5f33643c086570711b24e89b45daf343583fde79601384ea04c058168bd07a284d9a20d698e43eef494ef72c7b0d1b97
  languageName: node
  linkType: hard

"@ui5/webcomponents-icons-business-suite@npm:2.10.0":
  version: 2.10.0
  resolution: "@ui5/webcomponents-icons-business-suite@npm:2.10.0"
  dependencies:
    "@ui5/webcomponents-base": "npm:2.10.0"
  checksum: 10/a53d5044cfa8859a62509703d0da1555d8c1a375a7bc1491930ec7e794464e7188a3975e1ffaf2e7a766477d84d07cc2bd83352cf4e81293345084f13bd54ec5
  languageName: node
  linkType: hard

"@ui5/webcomponents-icons-tnt@npm:2.10.0":
  version: 2.10.0
  resolution: "@ui5/webcomponents-icons-tnt@npm:2.10.0"
  dependencies:
    "@ui5/webcomponents-base": "npm:2.10.0"
  checksum: 10/76922865d7b8524ab5e3aca3555a993b22d1afba4d61c3587971e785d0a4d374e85ff1d6492949a82cb0f52f1b0b587711e99ac5b31e17ccd13369581177d3f6
  languageName: node
  linkType: hard

"@ui5/webcomponents-icons@npm:2.10.0":
  version: 2.10.0
  resolution: "@ui5/webcomponents-icons@npm:2.10.0"
  dependencies:
    "@ui5/webcomponents-base": "npm:2.10.0"
  checksum: 10/416b34069fdf6ce86a32f9af71e148943b389e8b5036633ee5cbac7f764bfb94e42131f3ef3b9eb9681d284c554877618574b811094fb9f1e04cd37d60b13cb7
  languageName: node
  linkType: hard

"@ui5/webcomponents-localization@npm:2.10.0":
  version: 2.10.0
  resolution: "@ui5/webcomponents-localization@npm:2.10.0"
  dependencies:
    "@types/openui5": "npm:^1.113.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
  checksum: 10/d230a56aa3dd473b4a17189879163b02fa726b9db0fb61ffe1fb43380160cc08e10190c2ec01416bf1d9abe3db2cd6b42712939862438176b5ed430725dab553
  languageName: node
  linkType: hard

"@ui5/webcomponents-theming@npm:2.10.0":
  version: 2.10.0
  resolution: "@ui5/webcomponents-theming@npm:2.10.0"
  dependencies:
    "@sap-theming/theming-base-content": "npm:11.26.0"
    "@ui5/webcomponents-base": "npm:2.10.0"
  checksum: 10/592da4213e6bab3d840f88fee538a0e20085ebf1005173aeea969dadc743000207490726b775da9305bfea23588b480a06aeffa4458febb33bdee8b3bb829717
  languageName: node
  linkType: hard

"@ui5/webcomponents@npm:2.10.0":
  version: 2.10.0
  resolution: "@ui5/webcomponents@npm:2.10.0"
  dependencies:
    "@ui5/webcomponents-base": "npm:2.10.0"
    "@ui5/webcomponents-icons": "npm:2.10.0"
    "@ui5/webcomponents-icons-business-suite": "npm:2.10.0"
    "@ui5/webcomponents-icons-tnt": "npm:2.10.0"
    "@ui5/webcomponents-localization": "npm:2.10.0"
    "@ui5/webcomponents-theming": "npm:2.10.0"
  checksum: 10/f58912458a6f7647727dc8f11f1cc4d61a66e6b7252624477a099851a3f800bbe1e7828bd38f2f1559420f867c3723d3439081f580f4dcf674d24e2678ca4a7d
  languageName: node
  linkType: hard

"@uppy/aws-s3@npm:4.2.3":
  version: 4.2.3
  resolution: "@uppy/aws-s3@npm:4.2.3"
  dependencies:
    "@uppy/companion-client": "npm:^4.4.1"
    "@uppy/utils": "npm:^6.1.1"
  peerDependencies:
    "@uppy/core": ^4.4.1
  checksum: 10/4dedca502b5206d05cebce7bc68595bdba332a7730562a05f7a03f22af3cd3c35b5ff753a0bb9fbd6c651c12d430641fde20098a741f1a104328126816616f8a
  languageName: node
  linkType: hard

"@uppy/companion-client@npm:^4.4.1":
  version: 4.4.1
  resolution: "@uppy/companion-client@npm:4.4.1"
  dependencies:
    "@uppy/utils": "npm:^6.1.1"
    namespace-emitter: "npm:^2.0.1"
    p-retry: "npm:^6.1.0"
  peerDependencies:
    "@uppy/core": ^4.4.1
  checksum: 10/ef918ee141da9034352da8db2b466b386d26916104221e9052eecc9697f2ae9e67a9c05056f8f95da775c0a945998e97da9dd455739ec5b978eeed85eb1e761f
  languageName: node
  linkType: hard

"@uppy/core@npm:4.4.5":
  version: 4.4.5
  resolution: "@uppy/core@npm:4.4.5"
  dependencies:
    "@transloadit/prettier-bytes": "npm:^0.3.4"
    "@uppy/store-default": "npm:^4.2.0"
    "@uppy/utils": "npm:^6.1.4"
    lodash: "npm:^4.17.21"
    mime-match: "npm:^1.0.2"
    namespace-emitter: "npm:^2.0.1"
    nanoid: "npm:^5.0.9"
    preact: "npm:^10.5.13"
  checksum: 10/7a50e57c84f5ffb72263612c3c377b9c1e4208c603714b9c3c980fa5d2772d2f9d1ffa7afcd148142072ad2db95f8fe5b8390e23158dcca55cdfe1d0f1f5f42b
  languageName: node
  linkType: hard

"@uppy/dashboard@npm:4.3.4":
  version: 4.3.4
  resolution: "@uppy/dashboard@npm:4.3.4"
  dependencies:
    "@transloadit/prettier-bytes": "npm:^0.3.4"
    "@uppy/informer": "npm:^4.2.1"
    "@uppy/provider-views": "npm:^4.4.3"
    "@uppy/status-bar": "npm:^4.1.3"
    "@uppy/thumbnail-generator": "npm:^4.1.1"
    "@uppy/utils": "npm:^6.1.4"
    classnames: "npm:^2.2.6"
    lodash: "npm:^4.17.21"
    memoize-one: "npm:^6.0.0"
    nanoid: "npm:^5.0.9"
    preact: "npm:^10.5.13"
    shallow-equal: "npm:^3.0.0"
  peerDependencies:
    "@uppy/core": ^4.4.5
  checksum: 10/25a7d8e7b7762eb4b7f483a60d1ac68f87632356e5f52d810cef895bde8179f44d5897555e2d2469d5170d19968924895826798e6a3052dd4c7fb9d4cdebda2d
  languageName: node
  linkType: hard

"@uppy/drag-drop@npm:4.1.3":
  version: 4.1.3
  resolution: "@uppy/drag-drop@npm:4.1.3"
  dependencies:
    "@uppy/utils": "npm:^6.1.4"
    preact: "npm:^10.5.13"
  peerDependencies:
    "@uppy/core": ^4.4.5
  checksum: 10/5170caaf342eeb1302dfa81df930f8c0b3bd2358e12e871ed5553cfa86e1e0c8ad73cea428037e11656d5f69caae9bab2385d186bc14785336801397e3266505
  languageName: node
  linkType: hard

"@uppy/file-input@npm:4.1.3":
  version: 4.1.3
  resolution: "@uppy/file-input@npm:4.1.3"
  dependencies:
    "@uppy/utils": "npm:^6.1.4"
    preact: "npm:^10.5.13"
  peerDependencies:
    "@uppy/core": ^4.4.5
  checksum: 10/0f6e8c6bfffb8038a535a0ae48d20cd929bfcd7644d0b132c2b114dd586c9461f50637b27bf97853992d8a3df29da64a9519520d08de6813dcf32ec315cb1bbf
  languageName: node
  linkType: hard

"@uppy/informer@npm:^4.2.1":
  version: 4.2.1
  resolution: "@uppy/informer@npm:4.2.1"
  dependencies:
    "@uppy/utils": "npm:^6.1.1"
    preact: "npm:^10.5.13"
  peerDependencies:
    "@uppy/core": ^4.4.1
  checksum: 10/f61cc428c09b90a858c298be2643d010eed97d859180473b5f1c36a6b33769be349fcc0ce41f80015b5e980b4bbf3350c542e6fc100e701477a992fb0430b6f7
  languageName: node
  linkType: hard

"@uppy/progress-bar@npm:4.2.1":
  version: 4.2.1
  resolution: "@uppy/progress-bar@npm:4.2.1"
  dependencies:
    "@uppy/utils": "npm:^6.1.1"
    preact: "npm:^10.5.13"
  peerDependencies:
    "@uppy/core": ^4.4.1
  checksum: 10/c9059cff367ba65a1609d341ade7e1e593c9b7747becb71c6cb00048923b9d3aaaea346fa72438bd91d1e5b3e0fcdd918d95bd875262b98e62ae76ec7431f3db
  languageName: node
  linkType: hard

"@uppy/provider-views@npm:^4.4.3":
  version: 4.4.3
  resolution: "@uppy/provider-views@npm:4.4.3"
  dependencies:
    "@uppy/utils": "npm:^6.1.3"
    classnames: "npm:^2.2.6"
    nanoid: "npm:^5.0.9"
    p-queue: "npm:^8.0.0"
    preact: "npm:^10.5.13"
  peerDependencies:
    "@uppy/core": ^4.4.4
  checksum: 10/b95430d168073695728ae2bbc0854bd88b7bc53515103d51bf842c86d1d26b7ba0eb68b6f7292571490e15c50e8bc62641a48cd2c5a96b775c0866dfaa4a9758
  languageName: node
  linkType: hard

"@uppy/react@npm:4.2.3":
  version: 4.2.3
  resolution: "@uppy/react@npm:4.2.3"
  dependencies:
    "@uppy/utils": "npm:^6.1.3"
    use-sync-external-store: "npm:^1.2.0"
  peerDependencies:
    "@uppy/core": ^4.4.4
    "@uppy/dashboard": ^4.3.3
    "@uppy/drag-drop": ^4.1.2
    "@uppy/file-input": ^4.1.2
    "@uppy/progress-bar": ^4.2.1
    "@uppy/status-bar": ^4.1.3
    react: ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@uppy/dashboard":
      optional: true
    "@uppy/drag-drop":
      optional: true
    "@uppy/file-input":
      optional: true
    "@uppy/progress-bar":
      optional: true
    "@uppy/status-bar":
      optional: true
  checksum: 10/66ce49e9e6c7b367bb452daede4f5136ed58b489775a257d0914f08b2b3510c1f2f849d0fea4508c6b1e8d22a479ae5becbf5ca9e37f939a17d1ec83da1ba2e7
  languageName: node
  linkType: hard

"@uppy/status-bar@npm:^4.1.3":
  version: 4.1.3
  resolution: "@uppy/status-bar@npm:4.1.3"
  dependencies:
    "@transloadit/prettier-bytes": "npm:^0.3.4"
    "@uppy/utils": "npm:^6.1.3"
    classnames: "npm:^2.2.6"
    preact: "npm:^10.5.13"
  peerDependencies:
    "@uppy/core": ^4.4.4
  checksum: 10/d327e6611288290579ff621cd2bc479205ad5eb4585df9e3bd2c892cd7ebf086c218fcd03fd93f605d1a009a32424a792dc6117ced0a61b1812742961d276351
  languageName: node
  linkType: hard

"@uppy/store-default@npm:^4.2.0":
  version: 4.2.0
  resolution: "@uppy/store-default@npm:4.2.0"
  checksum: 10/9d00fe352c05f3d6b43e458ddf7160318e23275c0a53dddb614393b565dca11ec8bdc717e63131ab6387783f8a063a0f9452b70cf1d246e1a5f223251747cb67
  languageName: node
  linkType: hard

"@uppy/thumbnail-generator@npm:^4.1.1":
  version: 4.1.1
  resolution: "@uppy/thumbnail-generator@npm:4.1.1"
  dependencies:
    "@uppy/utils": "npm:^6.1.1"
    exifr: "npm:^7.0.0"
  peerDependencies:
    "@uppy/core": ^4.4.1
  checksum: 10/662cdbc5518dac999c639c3e5a80b4658eadb8be9eede37117d29c2fce575ccf65986e36612ad7fd5208a9088059c1bdbf1d827e6645b67c8eaaf49b8af3bf4f
  languageName: node
  linkType: hard

"@uppy/utils@npm:^6.1.1":
  version: 6.1.1
  resolution: "@uppy/utils@npm:6.1.1"
  dependencies:
    lodash: "npm:^4.17.21"
    preact: "npm:^10.5.13"
  checksum: 10/fa99685914c5b611f0b767e40e9884c14a292d61f2f4e7d3117325c64c6e5a546f944be21f0747de45f27cd055393e85db0161e0cc7afb3d372ad49cd5082de1
  languageName: node
  linkType: hard

"@uppy/utils@npm:^6.1.2":
  version: 6.1.2
  resolution: "@uppy/utils@npm:6.1.2"
  dependencies:
    lodash: "npm:^4.17.21"
    preact: "npm:^10.5.13"
  checksum: 10/debf9b10f5715cd804dc239ccea80bf99c8038531a75a908578f5433488ff18e2f1920ec3dda988b3347a8d7618d6114c8b4d91c084ed3814ad4d36bc5b2ef01
  languageName: node
  linkType: hard

"@uppy/utils@npm:^6.1.3":
  version: 6.1.3
  resolution: "@uppy/utils@npm:6.1.3"
  dependencies:
    lodash: "npm:^4.17.21"
    preact: "npm:^10.5.13"
  checksum: 10/bed9ebdc3c2394e21e95ecff80a7b0e806109576f4ec9b4219ee399ed0c222186d46dd8130aa46217eaee43b22a48598fd5c3b1d718c1b9b9d14e18bafce3e6e
  languageName: node
  linkType: hard

"@uppy/utils@npm:^6.1.4":
  version: 6.1.4
  resolution: "@uppy/utils@npm:6.1.4"
  dependencies:
    lodash: "npm:^4.17.21"
    preact: "npm:^10.5.13"
  checksum: 10/bdbf92a875a0b64fe7635d76a046d6cfe8fc062049f5fe870182a34bf7b12c3ec4f9843d4867df4b4c988c416ec967f96b989f84335c0ac8b5a764b4db194d42
  languageName: node
  linkType: hard

"@uppy/xhr-upload@npm:4.3.3":
  version: 4.3.3
  resolution: "@uppy/xhr-upload@npm:4.3.3"
  dependencies:
    "@uppy/companion-client": "npm:^4.4.1"
    "@uppy/utils": "npm:^6.1.2"
  peerDependencies:
    "@uppy/core": ^4.4.2
  checksum: 10/f2275bd6e6c4c24b428cce3b6ddc76edd4d8771469728e513ed1017574199b526d4ffcf0655f6f6232899eaaf0c543b834daf3422ece4b916259c73c41909aef
  languageName: node
  linkType: hard

"@viselect/vanilla@npm:3.9.0":
  version: 3.9.0
  resolution: "@viselect/vanilla@npm:3.9.0"
  checksum: 10/26de4616df76107d85f4506c5196aa3194c6ec624c65b8ff095f3a676b133705036f4ccc16dc5be26dead279f60165de9cae2bd381d43e988cb4c25e0f9e6c9a
  languageName: node
  linkType: hard

"@zxing/library@npm:^0.21.3":
  version: 0.21.3
  resolution: "@zxing/library@npm:0.21.3"
  dependencies:
    "@zxing/text-encoding": "npm:~0.9.0"
    ts-custom-error: "npm:^3.2.1"
  dependenciesMeta:
    "@zxing/text-encoding":
      optional: true
  checksum: 10/867e7a9be38ea8636050ab5852440e012525c01cd8791448afd15b8ec3ca41de6594202e3f8c90b2d3677b381a6a186eab79e5a431f23d09eb7887cdec772772
  languageName: node
  linkType: hard

"@zxing/text-encoding@npm:~0.9.0":
  version: 0.9.0
  resolution: "@zxing/text-encoding@npm:0.9.0"
  checksum: 10/268e4ef64b8eaa32b990240bdfd1f7b3e2b501a6ed866a565f7c9747f04ac884fbe0537fe12bb05d9241b98fb111270c0fd0023ef0a02d23a6619b4589e98f6b
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: 10/ca0a54e35bea4ece0ecb68a47b312e1a9a6f772408d5bcb9051230aaa94b0460671c5b5c9cb3240eb5b7bc94c52476550eb221f65a0bbd0145bdc9f3113a6707
  languageName: node
  linkType: hard

"adler-32@npm:~1.3.0":
  version: 1.3.1
  resolution: "adler-32@npm:1.3.1"
  checksum: 10/e11d70c113a49e6f04c249d8201813d46fcbc331e6c4593c926b9700e26a09898b3c0dea8595fd83dcb804bde359012f129d2322f7cc8177737aa14979490cc9
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10/3db6d8d4651f2aa1a9e4af35b96ab11a7607af57a24f3bc721a387eaa3b5f674e901f0a648b0caefd48f3fd117c7761b79a3b55854e2aebaa96c3f32cf76af84
  languageName: node
  linkType: hard

"ajv@npm:8.17.1":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    fast-uri: "npm:^3.0.1"
    json-schema-traverse: "npm:^1.0.0"
    require-from-string: "npm:^2.0.2"
  checksum: 10/ee3c62162c953e91986c838f004132b6a253d700f1e51253b99791e2dbfdb39161bc950ebdc2f156f8568035bb5ed8be7bd78289cd9ecbf3381fe8f5b82e3f33
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10/2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10/495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10/b4494dfbfc7e4591b4711a396bd27e540f8153914123dccb4cdbbcb514015ada63a3809f362b9d8d4f6b17a706f1d7bea3c6f974b15fa5ae76b5b502070889ff
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10/70fdf883b704d17a5dfc9cde206e698c16bcd74e7f196ab821511651aee4f9f76c9514bdfa6ca3a27b5e49138b89cb222a28caf3afe4567570139577f991df32
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10/3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"autoprefixer@npm:10.4.21":
  version: 10.4.21
  resolution: "autoprefixer@npm:10.4.21"
  dependencies:
    browserslist: "npm:^4.24.4"
    caniuse-lite: "npm:^1.0.30001702"
    fraction.js: "npm:^4.3.7"
    normalize-range: "npm:^0.1.2"
    picocolors: "npm:^1.1.1"
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.1.0
  bin:
    autoprefixer: bin/autoprefixer
  checksum: 10/5d7aeee78ef362a6838e12312908516a8ac5364414175273e5cff83bbff67612755b93d567f3aa01ce318342df48aeab4b291847b5800c780e58c458f61a98a6
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10/9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10/bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10/3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"bowser@npm:2.11.0":
  version: 2.11.0
  resolution: "bowser@npm:2.11.0"
  checksum: 10/ef46500eafe35072455e7c3ae771244e97827e0626686a9a3601c436d16eb272dad7ccbd49e2130b599b617ca9daa67027de827ffc4c220e02f63c84b69a8751
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10/a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10/fad11a0d4697a27162840b02b1fad249c1683cbc510cd5bf1a471f2f8085c046d41094308c577a50a03a579dd99d5a6b3724c4b5e8b14df2c4443844cfcda2c6
  languageName: node
  linkType: hard

"browserslist@npm:^4.0.0, browserslist@npm:^4.24.4":
  version: 4.24.4
  resolution: "browserslist@npm:4.24.4"
  dependencies:
    caniuse-lite: "npm:^1.0.30001688"
    electron-to-chromium: "npm:^1.5.73"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.1"
  bin:
    browserslist: cli.js
  checksum: 10/11fda105e803d891311a21a1f962d83599319165faf471c2d70e045dff82a12128f5b50b1fcba665a2352ad66147aaa248a9d2355a80aadc3f53375eb3de2e48
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.5":
  version: 4.24.5
  resolution: "browserslist@npm:4.24.5"
  dependencies:
    caniuse-lite: "npm:^1.0.30001716"
    electron-to-chromium: "npm:^1.5.149"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10/93fde829b77f20e2c4e1e0eaed154681c05e4828420e4afba790d480daa5de742977a44bbac8567881b8fbec3da3dea7ca1cb578ac1fd4385ef4ae91ca691d64
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10/ea026b27b13656330c2bbaa462a88181dcaa0435c1c2e705db89b31d9bdf7126049d6d0445ba746dca21454a0cfdf1d6f47fd39d34c8c8435296b30bc5738a13
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1":
  version: 1.0.1
  resolution: "call-bind-apply-helpers@npm:1.0.1"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10/6e30c621170e45f1fd6735e84d02ee8e02a3ab95cb109499d5308cbe5d1e84d0cd0e10b48cc43c76aa61450ae1b03a7f89c37c10fc0de8d4998b42aab0f268cc
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10/659b03c79bbfccf0cde3a79e7d52570724d7290209823e1ca5088f94b52192dc1836b82a324d0144612f816abb2f1734447438e38d9dafe0b3f82c2a1b9e3bce
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2":
  version: 1.0.3
  resolution: "call-bound@npm:1.0.3"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/c39a8245f68cdb7c1f5eea7b3b1e3a7a90084ea6efebb78ebc454d698ade2c2bb42ec033abc35f1e596d62496b6100e9f4cdfad1956476c510130e2cda03266d
  languageName: node
  linkType: hard

"caniuse-api@npm:^3.0.0":
  version: 3.0.0
  resolution: "caniuse-api@npm:3.0.0"
  dependencies:
    browserslist: "npm:^4.0.0"
    caniuse-lite: "npm:^1.0.0"
    lodash.memoize: "npm:^4.1.2"
    lodash.uniq: "npm:^4.5.0"
  checksum: 10/db2a229383b20d0529b6b589dde99d7b6cb56ba371366f58cbbfa2929c9f42c01f873e2b6ef641d4eda9f0b4118de77dbb2805814670bdad4234bf08e720b0b4
  languageName: node
  linkType: hard

"caniuse-lite@npm:1.0.30001720":
  version: 1.0.30001720
  resolution: "caniuse-lite@npm:1.0.30001720"
  checksum: 10/6557c5052fa17fd531f3e1a8013b5924fb69afcd53d9f3e3b9adc9e31c5a7e436b674c000c53659e097fe1fda1c290d1bd17c7f3f98d13749644386ed722ab5f
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.0, caniuse-lite@npm:^1.0.30001688":
  version: 1.0.30001692
  resolution: "caniuse-lite@npm:1.0.30001692"
  checksum: 10/92449ec9e9ac6cd8ce7ecc18a8759ae34e4b3ef412acd998714ee9d70dc286bc8d0d6e4917fa454798da9b37667eb5b3b41386bc9d25e4274d0b9c7af8339b0e
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001702":
  version: 1.0.30001704
  resolution: "caniuse-lite@npm:1.0.30001704"
  checksum: 10/76bf9a90aa49f04fd3c0224846f3c5d890b361a9931d43f5acbb8a16c622fa034cbf7951851ec7800d4b30b05cb7fd034a61dfc0b4db8b3af80eb19d041b7a21
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001716":
  version: 1.0.30001718
  resolution: "caniuse-lite@npm:1.0.30001718"
  checksum: 10/e172a4c156f743cc947e659f353ad9edb045725cc109a02cc792dcbf98569356ebfa4bb4356e3febf87427aab0951c34c1ee5630629334f25ae6f76de7d86fd0
  languageName: node
  linkType: hard

"cfb@npm:~1.2.1":
  version: 1.2.2
  resolution: "cfb@npm:1.2.2"
  dependencies:
    adler-32: "npm:~1.3.0"
    crc-32: "npm:~1.2.0"
  checksum: 10/e418fef195bb03bd30af18dbbccc1c21670194258cc1ac6e955becbded51fa5465ce195971252d941044b5978995483e881c9b0e40a90171debecb7ebb0b24be
  languageName: node
  linkType: hard

"chokidar@npm:^3.3.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10/c327fb07704443f8d15f7b4a7ce93b2f0bc0e6cea07ec28a7570aa22cd51fcf0379df589403976ea956c369f25aa82d84561947e227cd925902e1751371658df
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10/b63cb1f73d171d140a2ed8154ee6566c8ab775d3196b0e03a2a94b5f6a0ce7777ee5685ca56849403c8d17bd457a6540672f9a60696a6137c7a409097495b82c
  languageName: node
  linkType: hard

"classnames@npm:^2.2.6":
  version: 2.5.1
  resolution: "classnames@npm:2.5.1"
  checksum: 10/58eb394e8817021b153bb6e7d782cfb667e4ab390cb2e9dac2fc7c6b979d1cc2b2a733093955fc5c94aa79ef5c8c89f11ab77780894509be6afbb91dddd79d15
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10/eaa5561aeb3135c2cddf7a3b3f562fc4238ff3b3fc666869ef2adf264be0f372136702f16add9299087fb1907c2e4ec5dbfe83bd24bce815c70a80c6c1a2e950
  languageName: node
  linkType: hard

"clsx@npm:^1.0.4":
  version: 1.2.1
  resolution: "clsx@npm:1.2.1"
  checksum: 10/5ded6f61f15f1fa0350e691ccec43a28b12fb8e64c8e94715f2a937bc3722d4c3ed41d6e945c971fc4dcc2a7213a43323beaf2e1c28654af63ba70c9968a8643
  languageName: node
  linkType: hard

"codepage@npm:~1.15.0":
  version: 1.15.0
  resolution: "codepage@npm:1.15.0"
  checksum: 10/e4c07ac36eb528370c7acdf9e2bca44160444576d5d852dd5640531a6a427aa9ef2eb56b3c841a1638e807faa4975e2eeebfe57a5c48f123392250edc98a9398
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10/fa00c91b4332b294de06b443923246bccebe9fab1b253f7fe1772d37b06a2269b4039a85e309abe1fe11b267b11c08d1d0473fda3badd6167f57313af2887a64
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10/b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"colord@npm:^2.9.3":
  version: 2.9.3
  resolution: "colord@npm:2.9.3"
  checksum: 10/907a4506d7307e2f580b471b581e992181ed75ab0c6925ece9ca46d88161d2fc50ed15891cd0556d0d9321237ca75afc9d462e4c050b939ef88428517f047f30
  languageName: node
  linkType: hard

"commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 10/9973af10727ad4b44f26703bf3e9fdc323528660a7590efe3aa9ad5042b4584c0deed84ba443f61c9d6f02dade54a5a5d3c95e306a1e1630f8374ae6db16c06d
  languageName: node
  linkType: hard

"compute-scroll-into-view@npm:^3.1.0":
  version: 3.1.1
  resolution: "compute-scroll-into-view@npm:3.1.1"
  checksum: 10/b68827555c39862cf3d7def838f3b8ee3751e3e88b9ec3bb601484666f0596963cd91db16b23248e14759339cf2ddff72b9c53c3070f6fd27177393ea83185f3
  languageName: node
  linkType: hard

"computed-style@npm:~0.1.3":
  version: 0.1.4
  resolution: "computed-style@npm:0.1.4"
  checksum: 10/65f376479ef638768f5acb3fe79fa5e95d2f209400e0fc354eae775735ac5ad81b4cd2e951b3985a639f6029bf263e54edeb5630ee1555996df0f6a4c2951151
  languageName: node
  linkType: hard

"cookie@npm:^1.0.2":
  version: 1.0.2
  resolution: "cookie@npm:1.0.2"
  checksum: 10/f5817cdc84d8977761b12549eba29435e675e65c7fef172bc31737788cd8adc83796bf8abe6d950554e7987325ad2d9ac2971c5bd8ff0c4f81c145f82e4ab1be
  languageName: node
  linkType: hard

"core-js-pure@npm:^3.30.2":
  version: 3.40.0
  resolution: "core-js-pure@npm:3.40.0"
  checksum: 10/f539347fd2823a4ea341bb44ff66731ce323e9715f1dccdd618f5f41b72da2b53ebad53a9599b4e946aa820e0001ae09da066cae7e9c76d7692116c181b32dba
  languageName: node
  linkType: hard

"core-js@npm:3.37.0":
  version: 3.37.0
  resolution: "core-js@npm:3.37.0"
  checksum: 10/97feac0b54b95d928bda6a6e611cf34963a265a5fe8ab46ed35bbc9d32a14221bf6bede5d6cd4b0c0f30e8440cf1eff0c4f0c242d719c561e5dd73d3b005d63c
  languageName: node
  linkType: hard

"crc-32@npm:~1.2.0, crc-32@npm:~1.2.1":
  version: 1.2.2
  resolution: "crc-32@npm:1.2.2"
  bin:
    crc32: bin/crc32.njs
  checksum: 10/824f696a5baaf617809aa9cd033313c8f94f12d15ebffa69f10202480396be44aef9831d900ab291638a8022ed91c360696dd5b1ba691eb3f34e60be8835b7c3
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10/0d52657d7ae36eb130999dffff1168ec348687b48dd38e2ff59992ed916c88d328cf1d07ff4a4a10bc78de5e1c23f04b306d569e42f7a2293915c081e4dfee86
  languageName: node
  linkType: hard

"css-box-model@npm:^1.2.0":
  version: 1.2.1
  resolution: "css-box-model@npm:1.2.1"
  dependencies:
    tiny-invariant: "npm:^1.0.6"
  checksum: 10/54778883733e59058b5de983cf442b9db6c1494543d4d84a3defd05b51b991a1865f59e4ae424e733af2aa1fdb6e0bd905cb73db0e7e548fbd89853859fedc81
  languageName: node
  linkType: hard

"css-declaration-sorter@npm:^7.2.0":
  version: 7.2.0
  resolution: "css-declaration-sorter@npm:7.2.0"
  peerDependencies:
    postcss: ^8.0.9
  checksum: 10/2acb9c13f556fc8f05e601e66ecae4cfdec0ed50ca69f18177718ad5a86c3929f7d0a2cae433fd831b2594670c6e61d3a25c79aa7830be5828dcd9d29219d387
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-select@npm:5.1.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.1.0"
    domhandler: "npm:^5.0.2"
    domutils: "npm:^3.0.1"
    nth-check: "npm:^2.0.1"
  checksum: 10/d486b1e7eb140468218a5ab5af53257e01f937d2173ac46981f6b7de9c5283d55427a36715dc8decfc0c079cf89259ac5b41ef58f6e1a422eee44ab8bfdc78da
  languageName: node
  linkType: hard

"css-tree@npm:^2.3.1":
  version: 2.3.1
  resolution: "css-tree@npm:2.3.1"
  dependencies:
    mdn-data: "npm:2.0.30"
    source-map-js: "npm:^1.0.1"
  checksum: 10/e5e39b82eb4767c664fa5c2cd9968c8c7e6b7fd2c0079b52680a28466d851e2826d5e64699c449d933c0e8ca0554beca43c41a9fcb09fb6a46139d462dbdf0df
  languageName: node
  linkType: hard

"css-tree@npm:~2.2.0":
  version: 2.2.1
  resolution: "css-tree@npm:2.2.1"
  dependencies:
    mdn-data: "npm:2.0.28"
    source-map-js: "npm:^1.0.1"
  checksum: 10/1959c4b0e268bf8db1b3a1776a5ba9ae3a464ccd1226bfa62799cb0a3d0039006e21fb95cec4dec9d687a9a9b90f692dff2d230b631527ece700f4bfb419aaf3
  languageName: node
  linkType: hard

"css-what@npm:^6.1.0":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: 10/c67a3a2d0d81843af87f8bf0a4d0845b0f952377714abbb2884e48942409d57a2110eabee003609d02ee487b054614bdfcfc59ee265728ff105bd5aa221c1d0e
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: 10/0e161912c1306861d8f46e1883be1cbc8b1b2879f0f509287c0db71796e4ddfb97ac96bdfca38f77f452e2c10554e1bb5678c99b07a5cf947a12778f73e47e12
  languageName: node
  linkType: hard

"cssnano-preset-default@npm:^7.0.7":
  version: 7.0.7
  resolution: "cssnano-preset-default@npm:7.0.7"
  dependencies:
    browserslist: "npm:^4.24.5"
    css-declaration-sorter: "npm:^7.2.0"
    cssnano-utils: "npm:^5.0.1"
    postcss-calc: "npm:^10.1.1"
    postcss-colormin: "npm:^7.0.3"
    postcss-convert-values: "npm:^7.0.5"
    postcss-discard-comments: "npm:^7.0.4"
    postcss-discard-duplicates: "npm:^7.0.2"
    postcss-discard-empty: "npm:^7.0.1"
    postcss-discard-overridden: "npm:^7.0.1"
    postcss-merge-longhand: "npm:^7.0.5"
    postcss-merge-rules: "npm:^7.0.5"
    postcss-minify-font-values: "npm:^7.0.1"
    postcss-minify-gradients: "npm:^7.0.1"
    postcss-minify-params: "npm:^7.0.3"
    postcss-minify-selectors: "npm:^7.0.5"
    postcss-normalize-charset: "npm:^7.0.1"
    postcss-normalize-display-values: "npm:^7.0.1"
    postcss-normalize-positions: "npm:^7.0.1"
    postcss-normalize-repeat-style: "npm:^7.0.1"
    postcss-normalize-string: "npm:^7.0.1"
    postcss-normalize-timing-functions: "npm:^7.0.1"
    postcss-normalize-unicode: "npm:^7.0.3"
    postcss-normalize-url: "npm:^7.0.1"
    postcss-normalize-whitespace: "npm:^7.0.1"
    postcss-ordered-values: "npm:^7.0.2"
    postcss-reduce-initial: "npm:^7.0.3"
    postcss-reduce-transforms: "npm:^7.0.1"
    postcss-svgo: "npm:^7.0.2"
    postcss-unique-selectors: "npm:^7.0.4"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/1ca9b739531acc2dff66347cc1b6195da0549058b5b00b9d36c3f241535ad67476218f61201cfb15e8b460357ec42414aa53bf78a7f01ee26ac26b7852e6c244
  languageName: node
  linkType: hard

"cssnano-utils@npm:^5.0.1":
  version: 5.0.1
  resolution: "cssnano-utils@npm:5.0.1"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/cdf37315d3cf9726e10ce842b18e148e4df1d1d18d292540e724d5a96994901abc631c8894328c39ab70c864449a8a83f8fc117114fdcbade204e5e65898af90
  languageName: node
  linkType: hard

"cssnano@npm:7.0.7":
  version: 7.0.7
  resolution: "cssnano@npm:7.0.7"
  dependencies:
    cssnano-preset-default: "npm:^7.0.7"
    lilconfig: "npm:^3.1.3"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/c5b3123757834537f818e0f3eb6b20da51a194fefed599632f7ddd600c9e25d38abe38a22582a579660a49368a146c294e2096b2837cbeeda51ddfc85b108601
  languageName: node
  linkType: hard

"csso@npm:^5.0.5":
  version: 5.0.5
  resolution: "csso@npm:5.0.5"
  dependencies:
    css-tree: "npm:~2.2.0"
  checksum: 10/4036fb2b9f8ed6b948349136b39e0b19ffb5edee934893a37b55e9a116186c4ae2a9d3ba66fbdbc07fa44a853fb478cd2d8733e4743473dcd364e7f21444ff34
  languageName: node
  linkType: hard

"cssstyle@npm:^4.2.1":
  version: 4.2.1
  resolution: "cssstyle@npm:4.2.1"
  dependencies:
    "@asamuzakjp/css-color": "npm:^2.8.2"
    rrweb-cssom: "npm:^0.8.0"
  checksum: 10/e287234f2fd4feb1d79217480f48356f398cc11b9d17d39e6624f7dc1bf4b51d1e2c49f12b1a324834b445c17cbbf83ae5d3ba22c89a6b229f86bcebeda746a8
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10/f593cce41ff5ade23f44e77521e3a1bcc2c64107041e1bf6c3c32adc5187d0d60983292fda326154d20b01079e24931aa5b08e4467cc488b60bb1e7f6d478ade
  languageName: node
  linkType: hard

"d3-dispatch@npm:1 - 2":
  version: 2.0.0
  resolution: "d3-dispatch@npm:2.0.0"
  checksum: 10/6118a345270c901a585b517cbeaed2e8b956ac96250a1f8273c0371120f1ebebea64e0f9478c82c585c3ecbdaf7001afdbc8fe069db4015a56959a4cb77582d9
  languageName: node
  linkType: hard

"d3-force@npm:^2.1.1":
  version: 2.1.1
  resolution: "d3-force@npm:2.1.1"
  dependencies:
    d3-dispatch: "npm:1 - 2"
    d3-quadtree: "npm:1 - 2"
    d3-timer: "npm:1 - 2"
  checksum: 10/3b118067dca8ecd004ed46896eef6537400597bb1556e1f5f7eb6732579f34de07ed4d603ee1e67e3a5d066fc4d8219cc77a71b80bd90e41f35f313bb3db922b
  languageName: node
  linkType: hard

"d3-quadtree@npm:1 - 2, d3-quadtree@npm:^2.0.0":
  version: 2.0.0
  resolution: "d3-quadtree@npm:2.0.0"
  checksum: 10/59220bbd1ad8d7ba4eb1e9722db481848d977e9f1f767dc2d1ca07ab7fe184dd4f5fa72f2c555ad21f8905754c439a26ac60f29b990ba917618b581128ef552e
  languageName: node
  linkType: hard

"d3-timer@npm:1 - 2":
  version: 2.0.0
  resolution: "d3-timer@npm:2.0.0"
  checksum: 10/9fb62ea75899b47c43eafaa3ee84438dc85510403d5f03be3d6fd113837468a874ce90e77477b9e781c0a13d7ae7edefd9af3ae58acd8d880f6f71a4f0bc4bcb
  languageName: node
  linkType: hard

"dagre-compound@npm:^0.0.11":
  version: 0.0.11
  resolution: "dagre-compound@npm:0.0.11"
  peerDependencies:
    dagre: ^0.8.5
  checksum: 10/d94a1f8c9737e14771be93ef9bd1868732fd7bf465cb104efa72944e94f2509a3e84b977b795267e00fa3f756b31c599620a0077c9ee24f977177459ad74208d
  languageName: node
  linkType: hard

"data-urls@npm:^5.0.0":
  version: 5.0.0
  resolution: "data-urls@npm:5.0.0"
  dependencies:
    whatwg-mimetype: "npm:^4.0.0"
    whatwg-url: "npm:^14.0.0"
  checksum: 10/5c40568c31b02641a70204ff233bc4e42d33717485d074244a98661e5f2a1e80e38fe05a5755dfaf2ee549f2ab509d6a3af2a85f4b2ad2c984e5d176695eaf46
  languageName: node
  linkType: hard

"date-fns@npm:4.1.0":
  version: 4.1.0
  resolution: "date-fns@npm:4.1.0"
  checksum: 10/d5f6e9de5bbc52310f786099e18609289ed5e30af60a71e0646784c8185ddd1d0eebcf7c96b7faaaefc4a8366f3a3a4244d099b6d0866ee2bec80d1361e64342
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.3.4":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10/1847944c2e3c2c732514b93d11886575625686056cd765336212dc15de2d2b29612b6cd80e1afba767bb8e1803b778caf9973e98169ef1a24a7a7009e1820367
  languageName: node
  linkType: hard

"decimal.js@npm:^10.5.0":
  version: 10.5.0
  resolution: "decimal.js@npm:10.5.0"
  checksum: 10/714d49cf2f2207b268221795ede330e51452b7c451a0c02a770837d2d4faed47d603a729c2aa1d952eb6c4102d999e91c9b952c1aa016db3c5cba9fc8bf4cda2
  languageName: node
  linkType: hard

"deep-equal@npm:^1.0.1":
  version: 1.1.2
  resolution: "deep-equal@npm:1.1.2"
  dependencies:
    is-arguments: "npm:^1.1.1"
    is-date-object: "npm:^1.0.5"
    is-regex: "npm:^1.1.4"
    object-is: "npm:^1.1.5"
    object-keys: "npm:^1.1.1"
    regexp.prototype.flags: "npm:^1.5.1"
  checksum: 10/c9d2ed2a0d93a2ee286bdb320cd51c78cd4c310b2161d1ede6476b67ca1d73860e7ff63b10927830aa4b9eca2a48073cfa54c8c4a1b2246397bda618c2138e97
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10/abdcb2505d80a53524ba871273e5da75e77e52af9e15b3aa65d8aad82b8a3a424dad7aee2cc0b71470ac7acf501e08defac362e8b6a73cdb4309f028061df4ae
  languageName: node
  linkType: hard

"define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10/b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"dependency-graph@npm:^1.0.0":
  version: 1.0.0
  resolution: "dependency-graph@npm:1.0.0"
  checksum: 10/bb078703c1214e2bafeaab7bf5dd979e9a5be04439332e2e9ce60eebde9fb6d1c99a349fb4edeeb1506220c31a6b743dccfd2ca6608b962e9da4d54565bf95e6
  languageName: node
  linkType: hard

"dom-helpers@npm:^5.1.3":
  version: 5.2.1
  resolution: "dom-helpers@npm:5.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.8.7"
    csstype: "npm:^3.0.2"
  checksum: 10/bed2341adf8864bf932b3289c24f35fdd99930af77df46688abf2d753ff291df49a15850c874d686d9be6ec4e1c6835673906e64dbd8b2839d227f117a11fd41
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.2"
    entities: "npm:^4.2.0"
  checksum: 10/e3bf9027a64450bca0a72297ecdc1e3abb7a2912268a9f3f5d33a2e29c1e2c3502c6e9f860fc6625940bfe0cfb57a44953262b9e94df76872fdfb8151097eeb3
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10/ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:5.0.3, domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: "npm:^2.3.0"
  checksum: 10/809b805a50a9c6884a29f38aec0a4e1b4537f40e1c861950ed47d10b049febe6b79ab72adaeeebb3cc8fc1cd33f34e97048a72a9265103426d93efafa78d3e96
  languageName: node
  linkType: hard

"dompurify@npm:3.2.6":
  version: 3.2.6
  resolution: "dompurify@npm:3.2.6"
  dependencies:
    "@types/trusted-types": "npm:^2.0.7"
  dependenciesMeta:
    "@types/trusted-types":
      optional: true
  checksum: 10/b91631ed0e4d17fae950ef53613cc009ed7e73adc43ac94a41dd52f35483f7538d13caebdafa7626e0da145fc8184e7ac7935f14f25b7e841b32fda777e40447
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1, domutils@npm:^3.2.1":
  version: 3.2.2
  resolution: "domutils@npm:3.2.2"
  dependencies:
    dom-serializer: "npm:^2.0.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
  checksum: 10/2e08842151aa406f50fe5e6d494f4ec73c2373199fa00d1f77b56ec604e566b7f226312ae35ab8160bb7f27a27c7285d574c8044779053e499282ca9198be210
  languageName: node
  linkType: hard

"downshift@npm:9.0.9":
  version: 9.0.9
  resolution: "downshift@npm:9.0.9"
  dependencies:
    "@babel/runtime": "npm:^7.24.5"
    compute-scroll-into-view: "npm:^3.1.0"
    prop-types: "npm:^15.8.1"
    react-is: "npm:18.2.0"
    tslib: "npm:^2.6.2"
  peerDependencies:
    react: ">=16.12.0"
  checksum: 10/6abc7a585f002f0ebaba1ec42f6102b940257f294d0a33bf189387a316afa24254f0d49c4d5d2553a2263b4e42f497297a8c66dab9bebe24b3dce11fa1456d20
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10/5add88a3d68d42d6e6130a0cac450b7c2edbe73364bbd2fc334564418569bea97c6943a8fcd70e27130bf32afc236f30982fc4905039b703f23e9e0433c29934
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10/9b1d3e1baefeaf7d70799db8774149cef33b97183a6addceeba0cf6b85ba23ee2686f302f14482006df32df75d32b17c509c143a3689627929e4a8efaf483952
  languageName: node
  linkType: hard

"echarts@npm:5.6.0":
  version: 5.6.0
  resolution: "echarts@npm:5.6.0"
  dependencies:
    tslib: "npm:2.3.0"
    zrender: "npm:5.6.1"
  checksum: 10/e73344abb777fd8401c0b89a5d83b65c7a81a11540e2047d51f4aae9419baf4dc2524a5d9561f9ca0fe8d6b432c58b7a1d518f2a4338041046506db8257a1332
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.149":
  version: 1.5.152
  resolution: "electron-to-chromium@npm:1.5.152"
  checksum: 10/6e90f1f0f4f94aec25959e8fd6aee6558c5927acf737c9903862ffac659c9a238b603957f381eada918fd09a0af1ede2f81c6e45bbea8e152074dad0eeca47da
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.73":
  version: 1.5.83
  resolution: "electron-to-chromium@npm:1.5.83"
  checksum: 10/54326419778f80bfc3a76fec2e5a9122d81e7b04758da0b9c4d8bac612e6740f67f8a072b30ba62729f8ff5946fab3e2e1060936d1424eb5594c41baa9d82023
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10/c72d67a6821be15ec11997877c437491c313d924306b8da5d87d2a2bcc2cec9903cb5b04ee1a088460501d8e5b44f10df82fdc93c444101a7610b80c8b6938e1
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10/915acf859cea7131dac1b2b5c9c8e35c4849e325a1d114c30adb8cd615970f6dca0e27f64f3a4949d7d6ed86ecd79a1c5c63f02e697513cddd7b5835c90948b8
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10/bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"entities@npm:^4.2.0, entities@npm:^4.5.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10/ede2a35c9bce1aeccd055a1b445d41c75a14a2bb1cd22e242f20cf04d236cdcd7f9c859eb83f76885327bfae0c25bf03303665ee1ce3d47c5927b98b0e3e3d48
  languageName: node
  linkType: hard

"entities@npm:^6.0.0":
  version: 6.0.0
  resolution: "entities@npm:6.0.0"
  checksum: 10/cf37a4aad887ba8573532346da1c78349dccd5b510a9bbddf92fe59b36b18a8b26fe619a862de4e7fd3b8addc6d5e0969261198bbeb690da87297011a61b7066
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10/65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10/1d20d825cdcce8d811bfbe86340f4755c02655a7feb2f13f8c880566d9d72a3f6c92c192a6867632e490d6da67b678271f46e01044996a6443e870331100dfdd
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10/f8dc9e660d90919f11084db0a893128f3592b781ce967e4fccfb8f3106cb83e400a4032c559184ec52ee1dbd4b01e7776c7cd0b3327b1961b1a4a7008920fe78
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10/96e65d640156f91b707517e8cdc454dd7d47c32833aa3e85d79f24f9eb7ea85f39b63e36216ef0114996581969b59fe609a94e30316b08f5f4df1d44134cf8d5
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10/54fe77de288451dae51c37bfbfe3ec86732dc3778f98f3eb3bdb4bf48063b2c0b8f9c93542656986149d08aa5be3204286e2276053d19582b76753f1a2728867
  languageName: node
  linkType: hard

"esbuild@npm:0.25.5":
  version: 0.25.5
  resolution: "esbuild@npm:0.25.5"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.25.5"
    "@esbuild/android-arm": "npm:0.25.5"
    "@esbuild/android-arm64": "npm:0.25.5"
    "@esbuild/android-x64": "npm:0.25.5"
    "@esbuild/darwin-arm64": "npm:0.25.5"
    "@esbuild/darwin-x64": "npm:0.25.5"
    "@esbuild/freebsd-arm64": "npm:0.25.5"
    "@esbuild/freebsd-x64": "npm:0.25.5"
    "@esbuild/linux-arm": "npm:0.25.5"
    "@esbuild/linux-arm64": "npm:0.25.5"
    "@esbuild/linux-ia32": "npm:0.25.5"
    "@esbuild/linux-loong64": "npm:0.25.5"
    "@esbuild/linux-mips64el": "npm:0.25.5"
    "@esbuild/linux-ppc64": "npm:0.25.5"
    "@esbuild/linux-riscv64": "npm:0.25.5"
    "@esbuild/linux-s390x": "npm:0.25.5"
    "@esbuild/linux-x64": "npm:0.25.5"
    "@esbuild/netbsd-arm64": "npm:0.25.5"
    "@esbuild/netbsd-x64": "npm:0.25.5"
    "@esbuild/openbsd-arm64": "npm:0.25.5"
    "@esbuild/openbsd-x64": "npm:0.25.5"
    "@esbuild/sunos-x64": "npm:0.25.5"
    "@esbuild/win32-arm64": "npm:0.25.5"
    "@esbuild/win32-ia32": "npm:0.25.5"
    "@esbuild/win32-x64": "npm:0.25.5"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10/0fa4c3b42c6ddf1a008e75a4bb3dcab08ce22ac0b31dd59dc01f7fe8e21380bfaec07a2fe3730a7cf430da5a30142d016714b358666325a4733547afa42be405
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10/9d7169e3965b2f9ae46971afa392f6e5a25545ea30f2e2dd99c9b0a95a3f52b5653681a84f5b2911a413ddad2d7a93d3514165072f349b5ffc59c75a899970d6
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.0":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 10/8030029382404942c01d0037079f1b1bc8fed524b5849c237b80549b01e2fc49709e1d0c557fa65ca4498fc9e24cff1475ef7b855121fcc15f9d61f93e282346
  languageName: node
  linkType: hard

"eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 10/ac6423ec31124629c84c7077eed1e6987f6d66c31cf43c6fcbf6c87791d56317ce808d9ead483652436df171b526fc7220eccdc9f3225df334e81582c3cf7dd5
  languageName: node
  linkType: hard

"exifr@npm:^7.0.0":
  version: 7.1.3
  resolution: "exifr@npm:7.1.3"
  checksum: 10/c2f35d7aa317e7957f1a6aec035fd95beb6880547850234528b972c3449d16abc905a837a89390c9b681355dd24ca825043b14a648e3c6ade13c2c7a29ffbbf4
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 10/2d9bbb6473de7051f96790d5f9a678f32e60ed0aa70741dc7fdc96fec8d631124ec3374ac144387604f05afff9500f31a1d45bd9eee4cdc2e4f9ad2d9b9d5dbd
  languageName: node
  linkType: hard

"extend@npm:^3.0.2":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 10/59e89e2dc798ec0f54b36d82f32a27d5f6472c53974f61ca098db5d4648430b725387b53449a34df38fd0392045434426b012f302b3cc049a6500ccf82877e4e
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10/e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:1.1.2":
  version: 1.1.2
  resolution: "fast-diff@npm:1.1.2"
  checksum: 10/d212e75beac8f3f271f03c91068686a3a6bfa86af0c3f1348faf689c0fb2a31583a5234b0b4014918d2aff2076bbda900a8f3065ee498347709cc2528e970ebd
  languageName: node
  linkType: hard

"fast-diff@npm:^1.3.0":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: 10/9e57415bc69cd6efcc720b3b8fe9fdaf42dcfc06f86f0f45378b1fa512598a8aac48aa3928c8751d58e2f01bb4ba4f07e4f3d9bc0d57586d45f1bd1e872c6cde
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 10/43c87cd03926b072a241590e49eca0e2dfe1d347ddffd4b15307613b42b8eacce00a315cf3c7374736b5f343f27e27ec88726260eb03a758336d507d6fbaba0a
  languageName: node
  linkType: hard

"fast-xml-parser@npm:5.2.2":
  version: 5.2.2
  resolution: "fast-xml-parser@npm:5.2.2"
  dependencies:
    strnum: "npm:^2.1.0"
  bin:
    fxparser: src/cli/cli.js
  checksum: 10/d1a9a88d76dc6ce23d47836bbfc6239120211602833e64ca8bfd5f5f5ad1230128a033bbe136052e860c4d817f5385f4ec8bb3b31bb3ca0963e121e5d7b60264
  languageName: node
  linkType: hard

"fdir@npm:^6.4.3":
  version: 6.4.3
  resolution: "fdir@npm:6.4.3"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10/8e6d20f4590dc168de1374a9cadaa37e20ca6e0b822aa247c230e7ea1d9e9674a68cd816146435e4ecc98f9285091462ab7e5e56eebc9510931a1794e4db68b2
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10/a7095cb39e5bc32fada2aa7c7249d3f6b01bd1ce461a61b0adabacccabd9198500c6fb1f68a7c851a657e273fce2233ba869638897f3d7ed2e87a2d89b4436ea
  languageName: node
  linkType: hard

"focus-visible@npm:5.2.1":
  version: 5.2.1
  resolution: "focus-visible@npm:5.2.1"
  checksum: 10/b42a900fbccc05497b48a537b73c2ec41a209376f3df06ae908e151703921d0e62ae704a2e3a87bf86208e7f36a88fa3a672abdd629b5d7a6861c9ee3a399dca
  languageName: node
  linkType: hard

"fontfaceobserver@npm:2.3.0":
  version: 2.3.0
  resolution: "fontfaceobserver@npm:2.3.0"
  checksum: 10/fec6de6b7050856f9e5f03375d6dd762b66a7fe5c52516fa9a39ab8be8f6cb62abe758e4efaab2c24caeb03b2d992bdffb397dd016e537309d05ed6ade396b20
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    signal-exit: "npm:^4.0.1"
  checksum: 10/e3a60480f3a09b12273ce2c5fcb9514d98dd0e528f58656a1b04680225f918d60a2f81f6a368f2f3b937fcee9cfc0cbf16f1ad9a0bc6a3a6e103a84c9a90087e
  languageName: node
  linkType: hard

"frac@npm:~1.1.2":
  version: 1.1.2
  resolution: "frac@npm:1.1.2"
  checksum: 10/fbfbb28003bb84506dd35e7aad8543c5a358bdc95451d0065b6127d40d2c45106f14221575c3e9ce3ea4bf0bbf1225b73c5d655965c9f4ce44332cbe1b34667d
  languageName: node
  linkType: hard

"fraction.js@npm:^4.3.7":
  version: 4.3.7
  resolution: "fraction.js@npm:4.3.7"
  checksum: 10/bb5ebcdeeffcdc37b68ead3bdfc244e68de188e0c64e9702197333c72963b95cc798883ad16adc21588088b942bca5b6a6ff4aeb1362d19f6f3b629035dc15f5
  languageName: node
  linkType: hard

"fs-extra@npm:^11.0.0":
  version: 11.3.0
  resolution: "fs-extra@npm:11.3.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10/c9fe7b23dded1efe7bbae528d685c3206477e20cc60e9aaceb3f024f9b9ff2ee1f62413c161cb88546cc564009ab516dec99e9781ba782d869bb37e4fe04a97f
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/af143246cf6884fe26fa281621d45cfe111d34b30535a475bfa38dafe343dadb466c047a924ffc7d6b7b18265df4110224ce3803806dbb07173bf2087b648d7f
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/4c1ade961ded57cdbfbb5cac5106ec17bc8bccd62e16343c569a0ceeca83b9dfef87550b4dc5cbb89642da412b20c5071f304c8c464b80415446e8e155a038c0
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10/185e20d20f10c8d661d59aac0f3b63b31132d492e1b11fcc2a93cb2c47257ebaee7407c38513efd2b35cafdf972d9beb2ea4593c1e0f3bf8f2744836928d7454
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 10/0ddfd3ed1066a55984aaecebf5419fbd9344a5c38dd120ffb0739fac4496758dcf371297440528b115e4367fc46e3abc86a2cc0ff44612181b175ae967a11a05
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10/b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.6":
  version: 1.2.7
  resolution: "get-intrinsic@npm:1.2.7"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.0"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10/4f7149c9a826723f94c6d49f70bcb3df1d3f9213994fab3668f12f09fa72074681460fb29ebb6f135556ec6372992d63802386098791a8f09cfa6f27090fa67b
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"gl-matrix@npm:^3.1.0, gl-matrix@npm:^3.3.0":
  version: 3.4.3
  resolution: "gl-matrix@npm:3.4.3"
  checksum: 10/25ca2f7c762b7547cf462614ac7e0b98cffb131899f014e60a7e50395f4e9304efbb31dc73921e5af2a355cff663bee984d2710765f6730853e90d1c54d5d8e3
  languageName: node
  linkType: hard

"gl-vec2@npm:^1.3.0":
  version: 1.3.0
  resolution: "gl-vec2@npm:1.3.0"
  checksum: 10/6d5210d6c4288e1ea470c429d27967bec48a4305be2fa6a09db9f76d995b38eb78b71a27507d2703f97dad2376b317d7d2d970fdcc16feee6d463e592fe783fd
  languageName: node
  linkType: hard

"glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10/32cd106ce8c0d83731966d31517adb766d02c3812de49c30cfe0675c7c0ae6630c11214c54a5ae67aca882cf738d27fd7768f21aa19118b9245950554be07247
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10/698dfe11828b7efd0514cd11e573eaed26b2dff611f0400907281ce3eab0c1e56143ef9b35adc7c77ecc71fba74717b510c7c223d34ca8a98ec81777b293d4ac
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10/94e296d69f92dc1c0768fcfeecfb3855582ab59a7c75e969d5f96ce50c3d201fd86d5a2857c22565764d5bb8a816c7b1e58f133ec318cd56274da36c5e3fb1a1
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10/bf152d0ed1dc159239db1ba1f74fdbc40cb02f626770dcd5815c427ce0688c2635a06ed69af364396da4636d0408fcf7d4afdf7881724c3307e46aff30ca49e2
  languageName: node
  linkType: hard

"gridstack@npm:12.2.1":
  version: 12.2.1
  resolution: "gridstack@npm:12.2.1"
  checksum: 10/6ff400bcaf9f9d1ec2811940d3bdb3c1417b190239c2fa366e51bf153ddef8236cefe04125c8a2148621c533352dcf2c3483bcc0c53cac1b6281937d49844ab5
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10/2d8c9ab8cebb572e3362f7d06139a4592105983d4317e68f7adba320fe6ddfc8874581e0971e899e633fd5f72e262830edce36d5a0bc863dad17ad20572484b2
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10/959385c98696ebbca51e7534e0dc723ada325efa3475350951363cce216d27373e0259b63edb599f72eb94d6cde8577b4b2375f080b303947e560f85692834fa
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10/c74c5f5ceee3c8a5b8bc37719840dc3749f5b0306d818974141dda2471a1a2ca6c8e46b9d6ac222c5345df7a901c9b6f350b1e6d62763fec877e26609a401bfe
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10/7898a9c1788b2862cf0f9c345a6bec77ba4a0c0983c7f19d610c382343d4f98fa260686b225dfb1f88393a66679d2ec58ee310c1d6868c081eda7918f32cc70a
  languageName: node
  linkType: hard

"he@npm:1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 10/d09b2243da4e23f53336e8de3093e5c43d2c39f8d0d18817abfa32ce3e9355391b2edb4bb5edc376aea5d4b0b59d6a0482aab4c52bc02ef95751e4b818e847f1
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.3.0, hoist-non-react-statics@npm:^3.3.2":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: "npm:^16.7.0"
  checksum: 10/1acbe85f33e5a39f90c822ad4d28b24daeb60f71c545279431dc98c312cd28a54f8d64788e477fe21dc502b0e3cf58589ebe5c1ad22af27245370391c2d24ea6
  languageName: node
  linkType: hard

"html-dom-parser@npm:5.1.1":
  version: 5.1.1
  resolution: "html-dom-parser@npm:5.1.1"
  dependencies:
    domhandler: "npm:5.0.3"
    htmlparser2: "npm:10.0.0"
  checksum: 10/85bbaa8699d40d54fd64f5472088199178a6c3dd6a907358ac476d9c511cb8ab10bff1469a785411e8b831a73ac08348392e56076eab6d100a3ec19dccc722ad
  languageName: node
  linkType: hard

"html-encoding-sniffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "html-encoding-sniffer@npm:4.0.0"
  dependencies:
    whatwg-encoding: "npm:^3.1.1"
  checksum: 10/e86efd493293a5671b8239bd099d42128433bb3c7b0fdc7819282ef8e118a21f5dead0ad6f358e024a4e5c84f17ebb7a9b36075220fac0a6222b207248bede6f
  languageName: node
  linkType: hard

"html-react-parser@npm:5.2.5":
  version: 5.2.5
  resolution: "html-react-parser@npm:5.2.5"
  dependencies:
    domhandler: "npm:5.0.3"
    html-dom-parser: "npm:5.1.1"
    react-property: "npm:2.0.2"
    style-to-js: "npm:1.1.16"
  peerDependencies:
    "@types/react": 0.14 || 15 || 16 || 17 || 18 || 19
    react: 0.14 || 15 || 16 || 17 || 18 || 19
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/22852dc4826d3be9506e238e37a05c23b432675aac33f22bd5741caa195a32e99de99d2b99037fda532f198afbcdeacfc0b27627d6e529143a98c8f191e99c52
  languageName: node
  linkType: hard

"htmlparser2@npm:10.0.0":
  version: 10.0.0
  resolution: "htmlparser2@npm:10.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.2.1"
    entities: "npm:^6.0.0"
  checksum: 10/768870f0e020dca19dc45df206cb6ac466c5dba6566c8fca4ca880347eed409f9977028d08644ac516bca8628ac9c7ded5a3847dc3ee1c043f049abf9e817154
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 10/362d5ed66b12ceb9c0a328fb31200b590ab1b02f4a254a697dc796850cc4385603e75f53ec59f768b2dad3bfa1464bd229f7de278d2899a0e3beffc634b6683f
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0, http-proxy-agent@npm:^7.0.2":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10/d062acfa0cb82beeb558f1043c6ba770ea892b5fb7b28654dbc70ea2aeea55226dd34c02a294f6c1ca179a5aa483c4ea641846821b182edbd9cc5d89b54c6848
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1, https-proxy-agent@npm:^7.0.6":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10/784b628cbd55b25542a9d85033bdfd03d4eda630fb8b3c9477959367f3be95dc476ed2ecbb9836c359c7c698027fc7b45723a302324433590f45d6c1706e8c13
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10/24e3292dd3dadaa81d065c6f8c41b274a47098150d444b96e5f53b4638a9a71482921ea6a91a1f59bb71d9796de25e04afd05919fa64c360347ba65d3766f10f
  languageName: node
  linkType: hard

"imask@npm:7.6.1":
  version: 7.6.1
  resolution: "imask@npm:7.6.1"
  dependencies:
    "@babel/runtime-corejs3": "npm:^7.24.4"
  checksum: 10/0c8746547ee06132ae35200418cf6ed209e8f5bc7f6ba184e7b9aaa3932f518ac36531033da7ac2ab8a8c826098eaca85825b884b846867973cd31211dc5fc65
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10/2d30b157a91fe1c1d7c6f653cbf263f039be6c5bfa959245a16d4ee191fc0f2af86c08545b6e6beeb041c56b574d2d5b9f95343d378ab49c0f37394d541e7fc8
  languageName: node
  linkType: hard

"inline-style-parser@npm:0.2.4":
  version: 0.2.4
  resolution: "inline-style-parser@npm:0.2.4"
  checksum: 10/80814479d1f3c9cbd102f9de4cd6558cf43cc2e48640e81c4371c3634f1e8b6dfeb2f21063cfa31d46cc83e834c20cd59ed9eeed9bfd45ef5bc02187ad941faf
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10/1ed81e06721af012306329b31f532b5e24e00cb537be18ddc905a84f19fe8f83a09a1699862bf3a1ec4b9dea93c55a3fa5faf8b5ea380431469df540f38b092c
  languageName: node
  linkType: hard

"is-any-array@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-any-array@npm:2.0.1"
  checksum: 10/a2caaec75abb10ccb7e926aed322df5d2f206ae8645313771282702cf47d626832d9dc3318580e0fddbd04772d899263bebccb12c692e97988017ac549654cd4
  languageName: node
  linkType: hard

"is-arguments@npm:^1.1.1":
  version: 1.2.0
  resolution: "is-arguments@npm:1.2.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/471a8ef631b8ee8829c43a8ab05c081700c0e25180c73d19f3bf819c1a8448c426a9e8e601f278973eca68966384b16ceb78b8c63af795b099cd199ea5afc457
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10/078e51b4f956c2c5fd2b26bb2672c3ccf7e1faff38e0ebdba45612265f4e3d9fc3127a1fa8370bbf09eab61339203c3d3b7af5662cbf8be4030f8fac37745b0e
  languageName: node
  linkType: hard

"is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10/452b2c2fb7f889cbbf7e54609ef92cf6c24637c568acc7e63d166812a0fb365ae8a504c333a29add8bdb1686704068caa7f4e4b639b650dde4f00a038b8941fb
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/3a811b2c3176fb31abee1d23d3dc78b6c65fd9c07d591fcb67553cab9e7f272728c3dd077d2d738b53f9a2103255b0a6e8dfc9568a7805c56a78b2563e8d1dec
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10/df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10/44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.1, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10/3ed74f2b0cdf4f401f38edb0442ddfde3092d79d7d35c9919c86641efdbcbb32e45aa3c0f70ce5eecc946896cd5a0f26e4188b9f2b881876f7cb6c505b82da11
  languageName: node
  linkType: hard

"is-network-error@npm:^1.0.0":
  version: 1.1.0
  resolution: "is-network-error@npm:1.1.0"
  checksum: 10/b2fe6aac07f814a9de275efd05934c832c129e7ba292d27614e9e8eec9e043b7a0bbeaeca5d0916b0f462edbec2aa2eaee974ee0a12ac095040e9515c222c251
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10/6a6c3383f68afa1e05b286af866017c78f1226d43ac8cb064e115ff9ed85eb33f5c4f7216c96a71e4dfea289ef52c5da3aef5bbfade8ffe47a0465d70c0c8e86
  languageName: node
  linkType: hard

"is-potential-custom-element-name@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-potential-custom-element-name@npm:1.0.1"
  checksum: 10/ced7bbbb6433a5b684af581872afe0e1767e2d1146b2207ca0068a648fb5cab9d898495d1ac0583524faaf24ca98176a7d9876363097c2d14fee6dd324f3a1ab
  languageName: node
  linkType: hard

"is-regex@npm:^1.1.4":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10/c42b7efc5868a5c9a4d8e6d3e9816e8815c611b09535c00fead18a1138455c5cb5e1887f0023a467ad3f9c419d62ba4dc3d9ba8bafe55053914d6d6454a945d2
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10/7c9f715c03aff08f35e98b1fadae1b9267b38f0615d501824f9743f3aab99ef10e303ce7db3f186763a0b70a19de5791ebfc854ff884d5a8c4d92211f642ec92
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10/7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10/96f8786eaab98e4bf5b2a5d6d9588ea46c4d06bbc4f2eb861fdd7b6b182b16f71d8a70e79820f335d52653b16d4843b29dd9cdcf38ae80406756db9199497cf3
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10/af37d0d913fb56aec6dc0074c163cc71cd23c0b8aad5c2350747b6721d37ba118af35abdd8b33c47ec2800de07dedb16a527ca9c530ee004093e04958bd0cbf2
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10/bebe7ae829bbd586ce8cbe83501dd8cb8c282c8902a8aeeed0a073a89dc37e8103b1244f3c6acd60278bcbfe12d93a3f83c9ac396868a3b3bbc3c5e5e3b648ef
  languageName: node
  linkType: hard

"jsdom@npm:26.1.0":
  version: 26.1.0
  resolution: "jsdom@npm:26.1.0"
  dependencies:
    cssstyle: "npm:^4.2.1"
    data-urls: "npm:^5.0.0"
    decimal.js: "npm:^10.5.0"
    html-encoding-sniffer: "npm:^4.0.0"
    http-proxy-agent: "npm:^7.0.2"
    https-proxy-agent: "npm:^7.0.6"
    is-potential-custom-element-name: "npm:^1.0.1"
    nwsapi: "npm:^2.2.16"
    parse5: "npm:^7.2.1"
    rrweb-cssom: "npm:^0.8.0"
    saxes: "npm:^6.0.0"
    symbol-tree: "npm:^3.2.4"
    tough-cookie: "npm:^5.1.1"
    w3c-xmlserializer: "npm:^5.0.0"
    webidl-conversions: "npm:^7.0.0"
    whatwg-encoding: "npm:^3.1.1"
    whatwg-mimetype: "npm:^4.0.0"
    whatwg-url: "npm:^14.1.1"
    ws: "npm:^8.18.0"
    xml-name-validator: "npm:^5.0.0"
  peerDependencies:
    canvas: ^3.0.0
  peerDependenciesMeta:
    canvas:
      optional: true
  checksum: 10/39d78c4889cac20826393400dce1faed1666e9244fe0c8342a8f08c315375878e6be7fcfe339a33d6ff1a083bfe9e71b16d56ecf4d9a87db2da8c795925ea8c1
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 10/02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10/03014769e7dc77d4cf05fa0b534907270b60890085dd5e4d60a382ff09328580651da0b8b4cdf44d91e4c8ae64d91791d965f05707beff000ed494a38b6fec85
  languageName: node
  linkType: hard

"lilconfig@npm:^3.1.1, lilconfig@npm:^3.1.3":
  version: 3.1.3
  resolution: "lilconfig@npm:3.1.3"
  checksum: 10/b932ce1af94985f0efbe8896e57b1f814a48c8dbd7fc0ef8469785c6303ed29d0090af3ccad7e36b626bfca3a4dc56cc262697e9a8dd867623cf09a39d54e4c3
  languageName: node
  linkType: hard

"line-height@npm:0.3.1":
  version: 0.3.1
  resolution: "line-height@npm:0.3.1"
  dependencies:
    computed-style: "npm:~0.1.3"
  checksum: 10/e8b124a5f5fed314e3db4fe7b758952e0e64abecb04aaaea513a4cf7284ec7749176136f78fab07392fcf503008e201f991f94c2713113fe044ca2d3b694ca91
  languageName: node
  linkType: hard

"linkify-it@npm:5.0.0":
  version: 5.0.0
  resolution: "linkify-it@npm:5.0.0"
  dependencies:
    uc.micro: "npm:^2.0.0"
  checksum: 10/ef3b7609dda6ec0c0be8a7b879cea195f0d36387b0011660cd6711bba0ad82137f59b458b7e703ec74f11d88e7c1328e2ad9b855a8500c0ded67461a8c4519e6
  languageName: node
  linkType: hard

"lit-html@npm:^2.0.1":
  version: 2.8.0
  resolution: "lit-html@npm:2.8.0"
  dependencies:
    "@types/trusted-types": "npm:^2.0.2"
  checksum: 10/3503e55e2927c2ff94773cf041fc4128f92291869c9192f36eacb7f95132d11f6b329e5b910ab60a4456349cd2e6d23b33d83291b24d557bcd6b904d6314ac1a
  languageName: node
  linkType: hard

"lodash-es@npm:^4.17.15, lodash-es@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash-es@npm:4.17.21"
  checksum: 10/03f39878ea1e42b3199bd3f478150ab723f93cc8730ad86fec1f2804f4a07c6e30deaac73cad53a88e9c3db33348bb8ceeb274552390e7a75d7849021c02df43
  languageName: node
  linkType: hard

"lodash.clonedeep@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.clonedeep@npm:4.5.0"
  checksum: 10/957ed243f84ba6791d4992d5c222ffffca339a3b79dbe81d2eaf0c90504160b500641c5a0f56e27630030b18b8e971ea10b44f928a977d5ced3c8948841b555f
  languageName: node
  linkType: hard

"lodash.isequal@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.isequal@npm:4.5.0"
  checksum: 10/82fc58a83a1555f8df34ca9a2cd300995ff94018ac12cc47c349655f0ae1d4d92ba346db4c19bbfc90510764e0c00ddcc985a358bdcd4b3b965abf8f2a48a214
  languageName: node
  linkType: hard

"lodash.memoize@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.memoize@npm:4.1.2"
  checksum: 10/192b2168f310c86f303580b53acf81ab029761b9bd9caa9506a019ffea5f3363ea98d7e39e7e11e6b9917066c9d36a09a11f6fe16f812326390d8f3a54a1a6da
  languageName: node
  linkType: hard

"lodash.merge@npm:4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10/d0ea2dd0097e6201be083865d50c3fb54fbfbdb247d9cc5950e086c991f448b7ab0cdab0d57eacccb43473d3f2acd21e134db39f22dac2d6c9ba6bf26978e3d6
  languageName: node
  linkType: hard

"lodash.uniq@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.uniq@npm:4.5.0"
  checksum: 10/86246ca64ac0755c612e5df6d93cfe92f9ecac2e5ff054b965efbbb1d9a647b6310969e78545006f70f52760554b03233ad0103324121ae31474c20d5f7a2812
  languageName: node
  linkType: hard

"lodash@npm:^4.17.15, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10/c08619c038846ea6ac754abd6dd29d2568aa705feb69339e836dfa8d8b09abbb2f859371e86863eda41848221f9af43714491467b5b0299122431e202bb0c532
  languageName: node
  linkType: hard

"loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10/6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0, lru-cache@npm:^10.4.3":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10/e6e90267360476720fa8e83cc168aa2bf0311f3f2eea20a6ba78b90a885ae72071d9db132f40fda4129c803e7dcec3a6b6a6fbb44ca90b081630b810b5d6a41a
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10/fce0385840b6d86b735053dfe941edc2dd6468fda80fe74da1eeff10cbd82a75760f406194f2bc2fa85b99545b2bc1f84c08ddf994b21830775ba2d1a87e8bdf
  languageName: node
  linkType: hard

"marked@npm:15.0.12":
  version: 15.0.12
  resolution: "marked@npm:15.0.12"
  bin:
    marked: bin/marked.js
  checksum: 10/deeb619405c0c46af00c99b18b3365450abeb309104b24e3658f46142344f6b7c4117608c3b5834084d8738e92f81240c19f596e6ee369260f96e52b3457eaee
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10/11df2eda46d092a6035479632e1ec865b8134bdfc4bd9e571a656f4191525404f13a283a515938c3a8de934dbfd9c09674d9da9fa831e6eb7e22b50b197d2edd
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.28":
  version: 2.0.28
  resolution: "mdn-data@npm:2.0.28"
  checksum: 10/aec475e0c078af00498ce2f9434d96a1fdebba9814d14b8f72cd6d5475293f4b3972d0538af2d5c5053d35e1b964af08b7d162b98e9846e9343990b75e4baef1
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.30":
  version: 2.0.30
  resolution: "mdn-data@npm:2.0.30"
  checksum: 10/e4944322bf3e0461a2daa2aee7e14e208960a036289531e4ef009e53d32bd41528350c070c4a33be867980443fe4c0523518d99318423cffa7c825fe7b1154e2
  languageName: node
  linkType: hard

"memoize-one@npm:^5.1.1":
  version: 5.2.1
  resolution: "memoize-one@npm:5.2.1"
  checksum: 10/b7141dc148b5c6fdd51e77ecf0421fd2581681eb8756e0b3dfbd4fe765b5e2b5a6bc90214bb6f19a96b6aed44de17eda3407142a7be9e24ccd0774bbd9874d1b
  languageName: node
  linkType: hard

"memoize-one@npm:^6.0.0":
  version: 6.0.0
  resolution: "memoize-one@npm:6.0.0"
  checksum: 10/28feaf7e9a870efef1187df110b876ce42deaf86c955f4111d72d23b96e44eed573469316e6ad0d2cc7fa3b1526978215617b126158015f957242c7493babca9
  languageName: node
  linkType: hard

"mime-match@npm:^1.0.2":
  version: 1.0.2
  resolution: "mime-match@npm:1.0.2"
  dependencies:
    wildcard: "npm:^1.1.0"
  checksum: 10/3e4afd6be98e20bfb421146a14147560941f471886e6d3534372b37d29bb7e35a7462e1f9cee98312f92e44969ae9deca2da7ad91ab5a738af55a7d5f03a6814
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/dd6a8927b063aca6d910b119e1f2df6d2ce7d36eab91de83167dd136bb85e1ebff97b0d3de1cb08bd1f7e018ca170b4962479fefab5b2a69e2ae12cb2edc8348
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.0
  resolution: "minipass-fetch@npm:4.0.0"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10/4b0772dbee77727b469dc5bfc371541d9aba1e243fbb46ddc1b9ff7efa4de4a4cf5ff3a359d6a3b3a460ca26df9ae67a9c93be26ab6417c225e49d63b52b2801
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/40982d8d836a52b0f37049a0a7e5d0f089637298e6d9b45df9c115d4f0520682a78258905e5c8b180fb41b593b0a82cc1361d2c74b45f7ada66334f84d1ecfdd
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10/a5c6ef069f70d9a524d3428af39f2b117ff8cd84172e19b754e7264a33df460873e6eb3d6e55758531580970de50ae950c496256bb4ad3691a2974cddff189f0
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10/c25f0ee8196d8e6036661104bacd743785b2599a21de5c516b32b3fa2b83113ac89a2358465bc04956baab37ffb956ae43be679b2262bf7be15fce467ccd7950
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
    rimraf: "npm:^5.0.5"
  checksum: 10/622cb85f51e5c206a080a62d20db0d7b4066f308cb6ce82a9644da112367c3416ae7062017e631eb7ac8588191cfa4a9a279b8651c399265202b298e98c4acef
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10/16fd79c28645759505914561e249b9a1f5fe3362279ad95487a4501e4467abeb714fd35b95307326b8fd03f3c7719065ef11a6f97b7285d7888306d1bd2232ba
  languageName: node
  linkType: hard

"ml-array-max@npm:^1.2.4":
  version: 1.2.4
  resolution: "ml-array-max@npm:1.2.4"
  dependencies:
    is-any-array: "npm:^2.0.0"
  checksum: 10/d62689d349c825a36dae212ee109a7f81f2a3c1136cb79f50a90e6c48ae5acc78dacb1334864bfd9abf6f09b78ce6a68e2455b1259b775b3b05556362fbf8470
  languageName: node
  linkType: hard

"ml-array-min@npm:^1.2.3":
  version: 1.2.3
  resolution: "ml-array-min@npm:1.2.3"
  dependencies:
    is-any-array: "npm:^2.0.0"
  checksum: 10/bc6e0c69f20eb2b35c2c8d3a59f8c6289462683084e8fa50344ae4db3214b8b30854f96a8eaf22df510752c3065d3337cd996f98a80a95b47a88b38369beeb5b
  languageName: node
  linkType: hard

"ml-array-rescale@npm:^1.3.1":
  version: 1.3.7
  resolution: "ml-array-rescale@npm:1.3.7"
  dependencies:
    is-any-array: "npm:^2.0.0"
    ml-array-max: "npm:^1.2.4"
    ml-array-min: "npm:^1.2.3"
  checksum: 10/2f9883388ebb6c921c648a1cdcf1d853a35dff5f30e7204e142511abcafe05f40c1c1543232503aeff4ecdb3f57e958e7de8d070b973af3be7bfd27381b30528
  languageName: node
  linkType: hard

"ml-matrix@npm:6.5.0":
  version: 6.5.0
  resolution: "ml-matrix@npm:6.5.0"
  dependencies:
    ml-array-rescale: "npm:^1.3.1"
  checksum: 10/7e5825a8a26c30ecc437a2b4579dcf7bdac1b4c8b4aab85992e5529b4f8c0dbe583b70f68c3afe7ae60ec91bab685bf7f635fde3bd0239a2d89c561fd64d6ece
  languageName: node
  linkType: hard

"monaco-editor@npm:0.52.2":
  version: 0.52.2
  resolution: "monaco-editor@npm:0.52.2"
  checksum: 10/0d4962d69ffa0a8df040faa9c582cef1893fa3fb617feca8f1425c5e670e74c2856104b9a2b01cbda0103a5e5f92f58843206bc9a0e070471c0c1270d7f52a96
  languageName: node
  linkType: hard

"monaco-vim@npm:0.4.2":
  version: 0.4.2
  resolution: "monaco-vim@npm:0.4.2"
  peerDependencies:
    monaco-editor: "*"
  checksum: 10/43b9a4867a02534b55d81f5d674ba7ee13ba7356398f4dc4a857ae8c6988ea2d04ccff523b17f40d96c99d40445f73bc9f7b9c01f070e8155b146f019afb75ee
  languageName: node
  linkType: hard

"mousetrap@npm:^1.6.5":
  version: 1.6.5
  resolution: "mousetrap@npm:1.6.5"
  checksum: 10/2f55e715c8e106fa96135f8f3bc7fe71a3c497e803a451f357ae70598c3994a88c2907780f7abf51df4268c26cfb23f94db8687cd37154d65a0cc74cb530d438
  languageName: node
  linkType: hard

"ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10/aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"namespace-emitter@npm:^2.0.1":
  version: 2.0.1
  resolution: "namespace-emitter@npm:2.0.1"
  checksum: 10/996ba4c82a659ca523365e22ffa017a0156439384a3d0f1b8527a4ab6487277be7de6447474b7f77146ef931e8f6829be087352dfdbd586d4b7d0dd905953a18
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.11":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10/73b5afe5975a307aaa3c95dfe3334c52cdf9ae71518176895229b8d65ab0d1c0417dd081426134eb7571c055720428ea5d57c645138161e7d10df80815527c48
  languageName: node
  linkType: hard

"nanoid@npm:^5.0.9":
  version: 5.0.9
  resolution: "nanoid@npm:5.0.9"
  bin:
    nanoid: bin/nanoid.js
  checksum: 10/8a3f9104f81095e3e4785f58caae47a05755599824b8611b9730cbf73db706b664f100e6189f8303f08764f144d499613d8e4a39e83125c53f4b4986d6576621
  languageName: node
  linkType: hard

"nanopop@npm:2.4.2":
  version: 2.4.2
  resolution: "nanopop@npm:2.4.2"
  checksum: 10/61f74a2a2c833f9564a46c3bb9784471292467b239e428e6c28209bb68876ac40322b4c70122e0b0bbd1f7d4a71bd79ec447f36f2c7264a0bc9310bb97ad84eb
  languageName: node
  linkType: hard

"nats.ws@npm:1.30.3":
  version: 1.30.3
  resolution: "nats.ws@npm:1.30.3"
  dependencies:
    nkeys.js: "npm:1.1.0"
  dependenciesMeta:
    nkeys.js:
      optional: true
  checksum: 10/108a9b785078514763d077f2db5ba9f4bde3bf2b7c231ee99f7cda956a024d442e6d17648aacbc3b46bd74a1e0b0c2842fdcde4e7cc938858a09bad32ebc46a3
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10/b5734e87295324fabf868e36fb97c84b7d7f3156ec5f4ee5bf6e488079c11054f818290fc33804cef7b1ee21f55eeb14caea83e7dafae6492a409b3e573153e5
  languageName: node
  linkType: hard

"neotraverse@npm:0.6.18":
  version: 0.6.18
  resolution: "neotraverse@npm:0.6.18"
  checksum: 10/a19649cdadb9a3ce3c54c2d6093a2eb1e12364ace384301a7515d40c752bfbac45d12c6eb9c4b004beba7bd4d1871323ebd46ad1446e0de5bc5143b0367647cb
  languageName: node
  linkType: hard

"nkeys.js@npm:1.1.0":
  version: 1.1.0
  resolution: "nkeys.js@npm:1.1.0"
  dependencies:
    tweetnacl: "npm:1.0.3"
  checksum: 10/54ee6a141cadb1dd58d8739bb98424adbdb866256496e022c2ceee793581cfe774e51fd18b50563d4145ff647f670c73e30c0b164b0edfe4f91330187fe3eeb0
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.0.0
  resolution: "node-gyp@npm:11.0.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10/5d07430e887a906f85c7c6ed87e8facb7ecd4ce42d948a2438c471df2e24ae6af70f4def114ec1a03127988d164648dda8d75fe666f3c4b431e53856379fdf13
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10/c2b33b4f0c40445aee56141f13ca692fa6805db88510e5bbb3baadb2da13e1293b738e638e15e4a8eb668bb9e97debb08e7a35409b477b5cc18f171d35a83045
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.0.0
  resolution: "nopt@npm:8.0.0"
  dependencies:
    abbrev: "npm:^2.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10/2d137f64b6f9331ec97047dd1cbbe4dcd9a61ceef4fd0f2252c0bbac1d69ba15671e6fd83a441328824b3ca78afe6ebe1694f12ebcd162b73a221582a06179ff
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10/88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-range@npm:^0.1.2":
  version: 0.1.2
  resolution: "normalize-range@npm:0.1.2"
  checksum: 10/9b2f14f093593f367a7a0834267c24f3cb3e887a2d9809c77d8a7e5fd08738bcd15af46f0ab01cc3a3d660386f015816b5c922cea8bf2ee79777f40874063184
  languageName: node
  linkType: hard

"normalize-url@npm:^4.5.1":
  version: 4.5.1
  resolution: "normalize-url@npm:4.5.1"
  checksum: 10/20ced2845fcfaa46da74efc0aa39b7bed22f3db39e6e8b844261613082a36a2dcd468decad89fa9313b5464bebab4034f96bda7880e8fc468027fecf6a6fa254
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10/5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"numeral@npm:2.0.6":
  version: 2.0.6
  resolution: "numeral@npm:2.0.6"
  checksum: 10/5d19829b5b8d6b503d3c0144fcbaf0f515f8666c87e96e9484a3742d0f6e13a14f763cad313dfaa0d968b3e1a65fc1d8162f4f0c715c3d2e1859ed27ed0834c2
  languageName: node
  linkType: hard

"nwsapi@npm:^2.2.16":
  version: 2.2.16
  resolution: "nwsapi@npm:2.2.16"
  checksum: 10/1e5e086cdd4ca4a45f414d37f49bf0ca81d84ed31c6871ac68f531917d2910845db61f77c6d844430dc90fda202d43fce9603024e74038675de95229eb834dba
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10/fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-is@npm:^1.1.5":
  version: 1.1.6
  resolution: "object-is@npm:1.1.6"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
  checksum: 10/4f6f544773a595da21c69a7531e0e1d6250670f4e09c55f47eb02c516035cfcb1b46ceb744edfd3ecb362309dbccb6d7f88e43bf42e4d4595ac10a329061053a
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10/3d81d02674115973df0b7117628ea4110d56042e5326413e4b4313f0bcdf7dd78d4a3acef2c831463fa3796a66762c49daef306f4a0ea1af44877d7086d73bde
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10/2ef48ccfc6dd387253d71bf502604f7893ed62090b2c9d73387f10006c342606b05233da0e4f29388227b61eb5aeface6197e166520c465c234552eeab2fe633
  languageName: node
  linkType: hard

"p-queue@npm:^8.0.0":
  version: 8.0.1
  resolution: "p-queue@npm:8.0.1"
  dependencies:
    eventemitter3: "npm:^5.0.1"
    p-timeout: "npm:^6.1.2"
  checksum: 10/8dcf8fbb8339675eba7d369f6eebac9e249e2412280ad73938403b83a28a2627a2072c732890d22ef98837cef89ff09eecd839cbac358cdc532d00ef4f736d0d
  languageName: node
  linkType: hard

"p-retry@npm:^6.1.0":
  version: 6.2.1
  resolution: "p-retry@npm:6.2.1"
  dependencies:
    "@types/retry": "npm:0.12.2"
    is-network-error: "npm:^1.0.0"
    retry: "npm:^0.13.1"
  checksum: 10/7104ef13703b155d70883b0d3654ecc03148407d2711a4516739cf93139e8bec383451e14925e25e3c1ae04dbace3ed53c26dc3853c1e9b9867fcbdde25f4cdc
  languageName: node
  linkType: hard

"p-timeout@npm:^6.1.2":
  version: 6.1.4
  resolution: "p-timeout@npm:6.1.4"
  checksum: 10/5ee0df408ba353cc2d7036af90d2eb1724c428fd1cf67cd9110c03f0035077c29f6506bff7198dfbef4910ec558c711f21f9741d89d043a6f2c2ff82064afcaf
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10/58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"papaparse@npm:5.5.3":
  version: 5.5.3
  resolution: "papaparse@npm:5.5.3"
  checksum: 10/383e38f8ff910be48b684e7f47b84dbe5e67708669c13bb21f316a0591ef183ff670dfc7eb9f6d0a8b67e21a5b0e8934712cda96ff8fb678d6591e597d108bd1
  languageName: node
  linkType: hard

"parchment@npm:^1.1.2":
  version: 1.1.4
  resolution: "parchment@npm:1.1.4"
  checksum: 10/fc655f2bc608fe9e818f664af5f8debc553f84be3b3fd1d5f59a91a21caeacfc1237c8a01cb0ce2aafff123740d67e3f33a12b29c804ac0a131fe97986ba52dd
  languageName: node
  linkType: hard

"parchment@npm:^3.0.0":
  version: 3.0.0
  resolution: "parchment@npm:3.0.0"
  checksum: 10/67b91be827794c146b36bf56aa958e7519c8c47ff5298d7008eee3083ac33fdafdec4bb0f4b308d25b20c0e74775a4a729e280154209acf0e4d759d8ffc939ae
  languageName: node
  linkType: hard

"parse5@npm:^7.2.1":
  version: 7.2.1
  resolution: "parse5@npm:7.2.1"
  dependencies:
    entities: "npm:^4.5.0"
  checksum: 10/fd1a8ad1540d871e1ad6ca9bf5b67e30280886f1ce4a28052c0cb885723aa984d8cb1ec3da998349a6146960c8a84aa87b1a42600eb3b94495c7303476f2f88e
  languageName: node
  linkType: hard

"path-browserify@npm:1.0.1":
  version: 1.0.1
  resolution: "path-browserify@npm:1.0.1"
  checksum: 10/7e7368a5207e7c6b9051ef045711d0dc3c2b6203e96057e408e6e74d09f383061010d2be95cb8593fe6258a767c3e9fc6b2bfc7ce8d48ae8c3d9f6994cca9ad8
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10/55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10/49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10/5e8845c159261adda6f09814d7725683257fcc85a18f329880ab4d7cc1d12830967eae5d5894e453f341710d5484b8fdbbd4d75181b4d6e1eb2f4dc7aeadc434
  languageName: node
  linkType: hard

"pdfjs-dist@npm:5.3.31":
  version: 5.3.31
  resolution: "pdfjs-dist@npm:5.3.31"
  dependencies:
    "@napi-rs/canvas": "npm:^0.1.67"
  dependenciesMeta:
    "@napi-rs/canvas":
      optional: true
  checksum: 10/267e109c8410d83617d10bfb5b2cb8dc6f0219efab955ff766939fa56cd013b283f1a41dadd703c5725555ffb896ce8f0ffe559c3c73450a825fad1a7bb14469
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10/e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10/60c2595003b05e4535394d1da94850f5372c9427ca4413b71210f437f7b2ca091dbd611c45e8b37d10036fa8eade25c1b8951654f9d3973bfa66a2ff4d3b08bc
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10/ce617b8da36797d09c0baacb96ca8a44460452c89362d7cb8f70ca46b4158ba8bc3606912de7c818eb4a939f7f9015cef3c766ec8a0c6bfc725fdc078e39c717
  languageName: node
  linkType: hard

"pify@npm:^2.3.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 10/9503aaeaf4577acc58642ad1d25c45c6d90288596238fb68f82811c08104c800e5a7870398e9f015d82b44ecbcbef3dc3d4251a1cbb582f6e5959fe09884b2ba
  languageName: node
  linkType: hard

"pluralize-esm@npm:9.0.5":
  version: 9.0.5
  resolution: "pluralize-esm@npm:9.0.5"
  checksum: 10/e9cb52c4d7b3bf6f9b31150534320d7ea3bc8e2cc25bdc7d5e53d4494031d47a791b8678b71a2d40baddee227d81ff3bfcfde738fee1741d3f4064b21f3259ed
  languageName: node
  linkType: hard

"postcss-calc@npm:^10.1.1":
  version: 10.1.1
  resolution: "postcss-calc@npm:10.1.1"
  dependencies:
    postcss-selector-parser: "npm:^7.0.0"
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.38
  checksum: 10/16a25ec594cfbbda439fd2939820f78ed4e7b8b5ab458aed7283b05fffabe68e1d4e1f4821fac798095f10539371676cd690bd27927adefab1911ff69b33d62c
  languageName: node
  linkType: hard

"postcss-cli@npm:11.0.1":
  version: 11.0.1
  resolution: "postcss-cli@npm:11.0.1"
  dependencies:
    chokidar: "npm:^3.3.0"
    dependency-graph: "npm:^1.0.0"
    fs-extra: "npm:^11.0.0"
    picocolors: "npm:^1.0.0"
    postcss-load-config: "npm:^5.0.0"
    postcss-reporter: "npm:^7.0.0"
    pretty-hrtime: "npm:^1.0.3"
    read-cache: "npm:^1.0.0"
    slash: "npm:^5.0.0"
    tinyglobby: "npm:^0.2.12"
    yargs: "npm:^17.0.0"
  peerDependencies:
    postcss: ^8.0.0
  bin:
    postcss: index.js
  checksum: 10/149f66f50d87421423a56a42fdbbd4d5d64d502af8f2641cc0cb4bdeba8b4cfe4783a2a1d6ca5b374235af51055640ffd67e21252796861a379538c38be4263a
  languageName: node
  linkType: hard

"postcss-colormin@npm:^7.0.3":
  version: 7.0.3
  resolution: "postcss-colormin@npm:7.0.3"
  dependencies:
    browserslist: "npm:^4.24.5"
    caniuse-api: "npm:^3.0.0"
    colord: "npm:^2.9.3"
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/b9016d205eaf61a25efb187264a2ce35cb59aa1734b946268abcd747b5796e0d855c081b460ead4042a17c6806e011b57ee543b9e1f6312620f8daf661a7e40c
  languageName: node
  linkType: hard

"postcss-convert-values@npm:^7.0.5":
  version: 7.0.5
  resolution: "postcss-convert-values@npm:7.0.5"
  dependencies:
    browserslist: "npm:^4.24.5"
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/67920f9ba823a6f6aa3b46c3a098c2d4a7a2a32349971cfa6ce986e08e7cbae6badeb23de680d36d1439e7d3f2cdbf26f5ee080a66f2823931c1d3f8146bc2a6
  languageName: node
  linkType: hard

"postcss-discard-comments@npm:^7.0.4":
  version: 7.0.4
  resolution: "postcss-discard-comments@npm:7.0.4"
  dependencies:
    postcss-selector-parser: "npm:^7.1.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/a09ac248bfbd6f2baa72b84873a876f4113df0fb5e9dd10808f6bbb310473fcd7905cc4639dbfd3ad8a5444053d42f7bb644a6934e95305820bdedc731d3c80a
  languageName: node
  linkType: hard

"postcss-discard-duplicates@npm:^7.0.2":
  version: 7.0.2
  resolution: "postcss-discard-duplicates@npm:7.0.2"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/2da841b5c0117528e56e1ccda28924339c03fdb93dab61b767cebb9a9e4a2a077498d00e0c97c9ec36a534f98d6f358e6236f30913c184f90d51f6d302f4f0f6
  languageName: node
  linkType: hard

"postcss-discard-empty@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-discard-empty@npm:7.0.1"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/39977000657e78202da891ae6300593e40e1c8a756f1d9707087390e47a410739c394c35e902130556efb5808e6701b3b34b89facf7a9e56533d617dd9597049
  languageName: node
  linkType: hard

"postcss-discard-overridden@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-discard-overridden@npm:7.0.1"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/a0e67314b696591396e6bb371cdd57537e06f63e9fa0d742fe678decf600bed0cdcfa481487bce91b3732bdd7c46338f9102ccc8180c41032811e99962883715
  languageName: node
  linkType: hard

"postcss-import@npm:16.1.0":
  version: 16.1.0
  resolution: "postcss-import@npm:16.1.0"
  dependencies:
    postcss-value-parser: "npm:^4.0.0"
    read-cache: "npm:^1.0.0"
    resolve: "npm:^1.1.7"
  peerDependencies:
    postcss: ^8.0.0
  checksum: 10/a0877244976b8b8a930adfc8dff9f5f6c251d78649e67aa80e963d11821e6dbc8f8b16fa1d126e8725093d69c77486fc4a6861c823693c068c3192d4879e0b29
  languageName: node
  linkType: hard

"postcss-load-config@npm:^5.0.0":
  version: 5.1.0
  resolution: "postcss-load-config@npm:5.1.0"
  dependencies:
    lilconfig: "npm:^3.1.1"
    yaml: "npm:^2.4.2"
  peerDependencies:
    jiti: ">=1.21.0"
    postcss: ">=8.0.9"
    tsx: ^4.8.1
  peerDependenciesMeta:
    jiti:
      optional: true
    postcss:
      optional: true
    tsx:
      optional: true
  checksum: 10/d67b7db7599929e9fb305b49b25024b02b694eff8acbd86ec99dc4a13820edd77b34ec8bbc07ed3497f43c728565fec3d4c53582ffbc2c1ce97fd8c84be8e49c
  languageName: node
  linkType: hard

"postcss-merge-longhand@npm:^7.0.5":
  version: 7.0.5
  resolution: "postcss-merge-longhand@npm:7.0.5"
  dependencies:
    postcss-value-parser: "npm:^4.2.0"
    stylehacks: "npm:^7.0.5"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/3378fc3a196082dfdb9acff94efbfa0de95ed86bf87f485285e775fd3c21218e5a243e363ad80b96237edb454776f7c1deea28c37afb8b96ddfaf5cfe8bd606b
  languageName: node
  linkType: hard

"postcss-merge-rules@npm:^7.0.5":
  version: 7.0.5
  resolution: "postcss-merge-rules@npm:7.0.5"
  dependencies:
    browserslist: "npm:^4.24.5"
    caniuse-api: "npm:^3.0.0"
    cssnano-utils: "npm:^5.0.1"
    postcss-selector-parser: "npm:^7.1.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/fa490791ea5e907e4498701593252ce33df468a821e5f3acf5f126f73c8262189c13ca7a0c1645ae3d66a46a03cf930048e10d808182a3e9bec78af30a02893a
  languageName: node
  linkType: hard

"postcss-minify-font-values@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-minify-font-values@npm:7.0.1"
  dependencies:
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/6578a1fd293e202e738ce38d91d71c08ba970f4a998edff48022cb21ec23ef26bf7d284ddb41d6e51bf20b5b5676fe142de1bd092a76d2ef982d5ee1d6b00190
  languageName: node
  linkType: hard

"postcss-minify-gradients@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-minify-gradients@npm:7.0.1"
  dependencies:
    colord: "npm:^2.9.3"
    cssnano-utils: "npm:^5.0.1"
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/4aa782331c5d1826e549b3940eefb54e2d51f5c5a2c5f5537384bfe6eac45bfe7ba4535c03cd1642d8a27ab088f56c3682b55f5dd2c3f7969b715692e0c1102b
  languageName: node
  linkType: hard

"postcss-minify-params@npm:^7.0.3":
  version: 7.0.3
  resolution: "postcss-minify-params@npm:7.0.3"
  dependencies:
    browserslist: "npm:^4.24.5"
    cssnano-utils: "npm:^5.0.1"
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/97de22d6ba0310685d33b530dbfeefa930f7ac48effe623fc8a4a59d2b98bed221d0d2edad4f2e1f4590322240d0e1e94bdb162069c40b5d7ae00c58637c90c9
  languageName: node
  linkType: hard

"postcss-minify-selectors@npm:^7.0.5":
  version: 7.0.5
  resolution: "postcss-minify-selectors@npm:7.0.5"
  dependencies:
    cssesc: "npm:^3.0.0"
    postcss-selector-parser: "npm:^7.1.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/12580d9a17c146c9e9bb604b4887085d897554317590cee91e0f28e2a4757c18e09299365a44eae25e848e65d53b845928dfa56a9d0199d0e159d525732fbf89
  languageName: node
  linkType: hard

"postcss-normalize-charset@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-normalize-charset@npm:7.0.1"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/bcec822491e3421b009c688473433164b5c80bbef48af4e47f704bee68f0b7ba2009aaf46788e698dd233d5f4e1cf444a4f59a901623c73f8458c2227b15db57
  languageName: node
  linkType: hard

"postcss-normalize-display-values@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-normalize-display-values@npm:7.0.1"
  dependencies:
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/53f341c17a5487639e6f7c917ad695e059bf4aff66b3c971e008163f774337444753310def9f38dd26066ea96b136422592fc74077c38c40b3bfdfaa338d5b58
  languageName: node
  linkType: hard

"postcss-normalize-positions@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-normalize-positions@npm:7.0.1"
  dependencies:
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/72b23ab87c97c155d2ec475fba8a8b968f7c7b42d055a79b267449d570c328d5ea4cb0002428cf26e9daa70c58655e0b931d2a5801cc407554d3f03a21ac041b
  languageName: node
  linkType: hard

"postcss-normalize-repeat-style@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-normalize-repeat-style@npm:7.0.1"
  dependencies:
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/db677bceec8c00a1860b64932b99af937e7674b3e5c5ac333c95efb090e9abd747eca4ad51855f0fe73fbe544c3d21e58d06b39e03fd525945309743e31ec235
  languageName: node
  linkType: hard

"postcss-normalize-string@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-normalize-string@npm:7.0.1"
  dependencies:
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/48df2eaca6f5365af31ad46fd60a32dc7b714cc5ec8ba80980e65855ddc47c03ac82077ce7ca04c90898f73d173410d1d6a104754ff487e7e5a59e3eae8325b3
  languageName: node
  linkType: hard

"postcss-normalize-timing-functions@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-normalize-timing-functions@npm:7.0.1"
  dependencies:
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/31fb88489244334295918fa7d6af2d76c310a83abd20be0a7f1c408c54ac0c0f81b0ae7877698bf66de1f76495766e159c8871387407dfcafa0cb1a53f5f0460
  languageName: node
  linkType: hard

"postcss-normalize-unicode@npm:^7.0.3":
  version: 7.0.3
  resolution: "postcss-normalize-unicode@npm:7.0.3"
  dependencies:
    browserslist: "npm:^4.24.5"
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/fc10205655f77d6467da811fbd26aa607c519cbf162ae2ba40821cf64227233445490881119c820c6988c0943cb2f4dc755abe94cb30637001ca35cce5d07b61
  languageName: node
  linkType: hard

"postcss-normalize-url@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-normalize-url@npm:7.0.1"
  dependencies:
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/975dd0d1b55b637d45756ec57e554b2134f77368dd3ae09be9fa6636f2f41e72422505409d7fca75c635b9b1b8ec8ec2607d84c6c85497bbfd4e7748a2992882
  languageName: node
  linkType: hard

"postcss-normalize-whitespace@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-normalize-whitespace@npm:7.0.1"
  dependencies:
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/05a0fa74f4c8e93243053b9cc865cbddddb309b2ccb08271ca9c38ea7ece2ff43d5faa12cce87f06e40cbcf22c94443c9fa2b74ed0c6b94d72a9e67ea0381626
  languageName: node
  linkType: hard

"postcss-ordered-values@npm:^7.0.2":
  version: 7.0.2
  resolution: "postcss-ordered-values@npm:7.0.2"
  dependencies:
    cssnano-utils: "npm:^5.0.1"
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/be8fb13639fb0e1ffd7d4e9bb4824d3a283c8a63a8b0dd1a654435fff1e019007c79be877940bb101bb9ebd8ba3ac18bcffd144e939890bedeb40044dcc2b9cc
  languageName: node
  linkType: hard

"postcss-reduce-initial@npm:^7.0.3":
  version: 7.0.3
  resolution: "postcss-reduce-initial@npm:7.0.3"
  dependencies:
    browserslist: "npm:^4.24.5"
    caniuse-api: "npm:^3.0.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/8fd9ff4b49a2f7e1b7c51b7da637578e32a178363e3e932c80565241454dca306658dacd390ad3d73647d55dace8be8fe29278668afa32fd9d872ee7026bdbf7
  languageName: node
  linkType: hard

"postcss-reduce-transforms@npm:^7.0.1":
  version: 7.0.1
  resolution: "postcss-reduce-transforms@npm:7.0.1"
  dependencies:
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/a22d07559859b9d4313d579104a25aa254695bc37dec5134de1064d1bd52b9d1f33f050fbf330170ef1105ede9aad7741bbcf9cad2221a6a5c8d529fd3cf0259
  languageName: node
  linkType: hard

"postcss-reporter@npm:^7.0.0":
  version: 7.1.0
  resolution: "postcss-reporter@npm:7.1.0"
  dependencies:
    picocolors: "npm:^1.0.0"
    thenby: "npm:^1.3.4"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10/fc5dea0edf689d1cc68e83d761e7cee7b65dc264025e4ce249a817861a840c415a2cb841c3754a8b98966d89025dba10e73657b88827455010250aa7c5ed0bd8
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^7.0.0":
  version: 7.0.0
  resolution: "postcss-selector-parser@npm:7.0.0"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 10/0e92be7281e2b440a8be8cf207de40a24ca7bc765577916499614d5a47827a3e658206728cc559db96803e554270516104aad919a04f91bfa8914ccef1ba14ca
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^7.1.0":
  version: 7.1.0
  resolution: "postcss-selector-parser@npm:7.1.0"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 10/2caf09e66e2be81d45538f8afdc5439298c89bea71e9943b364e69dce9443d9c5ab33f4dd8b237f1ed7d2f38530338dcc189c1219d888159e6afb5b0afe58b19
  languageName: node
  linkType: hard

"postcss-svgo@npm:^7.0.2":
  version: 7.0.2
  resolution: "postcss-svgo@npm:7.0.2"
  dependencies:
    postcss-value-parser: "npm:^4.2.0"
    svgo: "npm:^3.3.2"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/8615877dffbac2bb2b971fb0e8c882ebff479c2529a0fc20937d09623fcaf35a2d934c4046188bae2534729aba1de5a1ba227630aaf96a800b6f2acdbfbf1d32
  languageName: node
  linkType: hard

"postcss-unique-selectors@npm:^7.0.4":
  version: 7.0.4
  resolution: "postcss-unique-selectors@npm:7.0.4"
  dependencies:
    postcss-selector-parser: "npm:^7.1.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/b880f96fdb20037b16ae21b48f5240a4cf8585bf3133c7894dd869711b14f3a1a82bbdecd36adc78f8c34553a46fc2199ed3e92d5031b0267ff6f43894fc00f7
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 10/e4e4486f33b3163a606a6ed94f9c196ab49a37a7a7163abfcd469e5f113210120d70b8dd5e33d64636f41ad52316a3725655421eb9a1094f1bcab1db2f555c62
  languageName: node
  linkType: hard

"postcss@npm:8.5.4":
  version: 8.5.4
  resolution: "postcss@npm:8.5.4"
  dependencies:
    nanoid: "npm:^3.3.11"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10/b037b55182aa3aa2f7c4ffb0f37518b35855bb0107222cc46e1b6fa13a4462cb2747d88d3bf66640e7cad20f465354b18d5eafffd44eba97364683a72a336695
  languageName: node
  linkType: hard

"preact@npm:^10.5.13":
  version: 10.25.4
  resolution: "preact@npm:10.25.4"
  checksum: 10/8b377438912965b45786d1dbfdd532157294a79bebc611b242d708317574403554ab37e34c613a3e9e862685c04695343f1426a38e12258e1e9ebafc0d7e4c5d
  languageName: node
  linkType: hard

"pretty-hrtime@npm:^1.0.3":
  version: 1.0.3
  resolution: "pretty-hrtime@npm:1.0.3"
  checksum: 10/0a462e88a0a3fd3320288fd8307f488974326ae8e13eea8c27f590f8ee767ccb59cf35bcae1cadff241cd8b72f3e373fc76ff1be95243649899bf8c816874af9
  languageName: node
  linkType: hard

"probe.gl@npm:^3.1.1":
  version: 3.6.0
  resolution: "probe.gl@npm:3.6.0"
  dependencies:
    "@babel/runtime": "npm:^7.0.0"
    "@probe.gl/env": "npm:3.6.0"
    "@probe.gl/log": "npm:3.6.0"
    "@probe.gl/stats": "npm:3.6.0"
  checksum: 10/5091a2d309adae50c5baeb098f2a6137fc8e0738e2db45e331a5e2de44ed9584c2cceeb4f542e3b817f28a887f6cda89a5347f28959ab9b667f6d0a0d9158613
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10/35610bdb0177d3ab5d35f8827a429fb1dc2518d9e639f2151ac9007f01a061c30e0c635a970c9b00c39102216160f6ec54b62377c92fac3b7bfc2ad4b98d195c
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10/96e1a82453c6c96eef53a37a1d6134c9f2482f94068f98a59145d0986ca4e497bf110a410adf73857e588165eab3899f0ebcf7b3890c1b3ce802abc0d65967d4
  languageName: node
  linkType: hard

"prop-types@npm:^15.6.0, prop-types@npm:^15.7.2, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 10/7d959caec002bc964c86cdc461ec93108b27337dabe6192fb97d69e16a0c799a03462713868b40749bfc1caf5f57ef80ac3e4ffad3effa636ee667582a75e2c0
  languageName: node
  linkType: hard

"punycode@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10/febdc4362bead22f9e2608ff0171713230b57aff9dddc1c273aa2a651fbd366f94b7d6a71d78342a7c0819906750351ca7f2edd26ea41b626d87d6a13d1bd059
  languageName: node
  linkType: hard

"quill-delta@npm:^3.6.2":
  version: 3.6.3
  resolution: "quill-delta@npm:3.6.3"
  dependencies:
    deep-equal: "npm:^1.0.1"
    extend: "npm:^3.0.2"
    fast-diff: "npm:1.1.2"
  checksum: 10/8aaa7bddab343f7ad613c57f50fda0406aed81f280130d7851a67e61e6614d8af29b9f341101480cf12477f81c73ee37d126dffc65fa9b3860ebfec855b03944
  languageName: node
  linkType: hard

"quill-delta@npm:^5.1.0":
  version: 5.1.0
  resolution: "quill-delta@npm:5.1.0"
  dependencies:
    fast-diff: "npm:^1.3.0"
    lodash.clonedeep: "npm:^4.5.0"
    lodash.isequal: "npm:^4.5.0"
  checksum: 10/6cd9a634dd729504b0d13b25718f3dfa6b54fc5144cc682d32f229c5da2e50726222858f9f6612a676f9e2b0a2ec3173d3d678fdc96b1040ba725bfc35af3fbe
  languageName: node
  linkType: hard

"quill-magic-url@npm:4.2.0":
  version: 4.2.0
  resolution: "quill-magic-url@npm:4.2.0"
  dependencies:
    "@types/quill": "npm:^2.0.9"
    normalize-url: "npm:^4.5.1"
    quill-delta: "npm:^3.6.2"
  checksum: 10/aa302149957381cb65fe9c0b5917788ccc49a1077828895ac519519a44a820296fad53a0e726604764844fd356cbb9424befadc77269130646a6c9586228359c
  languageName: node
  linkType: hard

"quill@npm:2.0.3":
  version: 2.0.3
  resolution: "quill@npm:2.0.3"
  dependencies:
    eventemitter3: "npm:^5.0.1"
    lodash-es: "npm:^4.17.21"
    parchment: "npm:^3.0.0"
    quill-delta: "npm:^5.1.0"
  checksum: 10/e4207da2250c3a194eb02f2506c2af06c27633772c2f5f7da45f48913f34c00d313bb2bcbb27a8473b8a55a7d7d9a1ae4966417a5038059cd1c7fb14bd793f13
  languageName: node
  linkType: hard

"raf-schd@npm:^4.0.2":
  version: 4.0.3
  resolution: "raf-schd@npm:4.0.3"
  checksum: 10/45514041c5ad31fa96aef3bb3c572a843b92da2f2cd1cb4a47c9ad58e48761d3a4126e18daa32b2bfa0bc2551a42d8f324a0e40e536cb656969929602b4e8b58
  languageName: node
  linkType: hard

"react-beautiful-dnd@npm:13.1.1":
  version: 13.1.1
  resolution: "react-beautiful-dnd@npm:13.1.1"
  dependencies:
    "@babel/runtime": "npm:^7.9.2"
    css-box-model: "npm:^1.2.0"
    memoize-one: "npm:^5.1.1"
    raf-schd: "npm:^4.0.2"
    react-redux: "npm:^7.2.0"
    redux: "npm:^4.0.4"
    use-memo-one: "npm:^1.1.1"
  peerDependencies:
    react: ^16.8.5 || ^17.0.0 || ^18.0.0
    react-dom: ^16.8.5 || ^17.0.0 || ^18.0.0
  checksum: 10/2de8162a74f7fc78294e5a928b92d3fff02c579d137a25d53b1ab4313abeb108709bb7281512f7f94d18257de3122b8c85cb5a8375113cb8657088b1a9bda65b
  languageName: node
  linkType: hard

"react-dom@npm:19.1.0":
  version: 19.1.0
  resolution: "react-dom@npm:19.1.0"
  dependencies:
    scheduler: "npm:^0.26.0"
  peerDependencies:
    react: ^19.1.0
  checksum: 10/c5b58605862c7b0bb044416b01c73647bb8e89717fb5d7a2c279b11815fb7b49b619fe685c404e59f55eb52c66831236cc565c25ee1c2d042739f4a2cc538aa2
  languageName: node
  linkType: hard

"react-image-crop@npm:11.0.10":
  version: 11.0.10
  resolution: "react-image-crop@npm:11.0.10"
  peerDependencies:
    react: ">=16.13.1"
  checksum: 10/5f38841ec89ac346a69e9bc3460eec71ccb8e154ef7364e7d8f87f902f6ecde2746dfd887230826d3d4fbe23314aa20cd19f80061bd9d3772d4464ba020987b8
  languageName: node
  linkType: hard

"react-is@npm:18.2.0":
  version: 18.2.0
  resolution: "react-is@npm:18.2.0"
  checksum: 10/200cd65bf2e0be7ba6055f647091b725a45dd2a6abef03bf2380ce701fd5edccee40b49b9d15edab7ac08a762bf83cb4081e31ec2673a5bfb549a36ba21570df
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1, react-is@npm:^16.7.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10/5aa564a1cde7d391ac980bedee21202fc90bdea3b399952117f54fb71a932af1e5902020144fb354b4690b2414a0c7aafe798eb617b76a3d441d956db7726fdf
  languageName: node
  linkType: hard

"react-is@npm:^17.0.2":
  version: 17.0.2
  resolution: "react-is@npm:17.0.2"
  checksum: 10/73b36281e58eeb27c9cc6031301b6ae19ecdc9f18ae2d518bdb39b0ac564e65c5779405d623f1df9abf378a13858b79442480244bd579968afc1faf9a2ce5e05
  languageName: node
  linkType: hard

"react-lifecycles-compat@npm:^3.0.4":
  version: 3.0.4
  resolution: "react-lifecycles-compat@npm:3.0.4"
  checksum: 10/c66b9c98c15cd6b0d0a4402df5f665e8cc7562fb7033c34508865bea51fd7b623f7139b5b7e708515d3cd665f264a6a9403e1fa7e6d61a05759066f5e9f07783
  languageName: node
  linkType: hard

"react-property@npm:2.0.2":
  version: 2.0.2
  resolution: "react-property@npm:2.0.2"
  checksum: 10/3a4bc1951b2b7992cb8a2d3f12016dd0920d1c06eb58b456204a6ae1210401d62baece098d3200ed8a0513dde247a5d96ffdb24f354e32ce5a9b26fbd8552668
  languageName: node
  linkType: hard

"react-redux@npm:^7.2.0":
  version: 7.2.9
  resolution: "react-redux@npm:7.2.9"
  dependencies:
    "@babel/runtime": "npm:^7.15.4"
    "@types/react-redux": "npm:^7.1.20"
    hoist-non-react-statics: "npm:^3.3.2"
    loose-envify: "npm:^1.4.0"
    prop-types: "npm:^15.7.2"
    react-is: "npm:^17.0.2"
  peerDependencies:
    react: ^16.8.3 || ^17 || ^18
  peerDependenciesMeta:
    react-dom:
      optional: true
    react-native:
      optional: true
  checksum: 10/1c3018bd2601e6d18339281867910b583dcbb3d8856403086e08c00abf0dfe467a94c0d1356bafa8cdf107bf1e2c9899a28486e4778e85c8bc4dfed2076b116f
  languageName: node
  linkType: hard

"react-truncate-markup@npm:5.1.2":
  version: 5.1.2
  resolution: "react-truncate-markup@npm:5.1.2"
  dependencies:
    line-height: "npm:0.3.1"
    memoize-one: "npm:^5.1.1"
    prop-types: "npm:^15.6.0"
    resize-observer-polyfill: "npm:1.5.x"
  peerDependencies:
    react: ">=16.3"
  checksum: 10/3afcf3d531e53760af79192d2d886f80d80b848bb10745f9c56c7ff64ddce98a4849935dbe7d0d0cdaaef7e388a1676b802fc07272a7ef2f47f323af59835b80
  languageName: node
  linkType: hard

"react-virtualized@npm:9.22.6":
  version: 9.22.6
  resolution: "react-virtualized@npm:9.22.6"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    clsx: "npm:^1.0.4"
    dom-helpers: "npm:^5.1.3"
    loose-envify: "npm:^1.4.0"
    prop-types: "npm:^15.7.2"
    react-lifecycles-compat: "npm:^3.0.4"
  peerDependencies:
    react: ^16.3.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^16.3.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10/57c24aa6226e628d970dd57b5d20693aec1ebc456c33667833779d26b82addbcbf03b65d874ad25154fb65a070a6a52d3dbdc699932442f1862246ee5d7e328c
  languageName: node
  linkType: hard

"react@npm:19.1.0":
  version: 19.1.0
  resolution: "react@npm:19.1.0"
  checksum: 10/d0180689826fd9de87e839c365f6f361c561daea397d61d724687cae88f432a307d1c0f53a0ee95ddbe3352c10dac41d7ff1ad85530fb24951b27a39e5398db4
  languageName: node
  linkType: hard

"read-cache@npm:^1.0.0":
  version: 1.0.0
  resolution: "read-cache@npm:1.0.0"
  dependencies:
    pify: "npm:^2.3.0"
  checksum: 10/83a39149d9dfa38f0c482ea0d77b34773c92fef07fe7599cdd914d255b14d0453e0229ef6379d8d27d6947f42d7581635296d0cfa7708f05a9bd8e789d398b31
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10/196b30ef6ccf9b6e18c4e1724b7334f72a093d011a99f3b5920470f0b3406a51770867b3e1ae9711f227ef7a7065982f6ee2ce316746b2cb42c88efe44297fe7
  languageName: node
  linkType: hard

"redux@npm:^4.0.0, redux@npm:^4.0.4":
  version: 4.2.1
  resolution: "redux@npm:4.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.9.2"
  checksum: 10/371e4833b671193303a7dea7803c8fdc8e0d566740c78f580e0a3b77b4161da25037626900a2205a5d616117fa6ad09a4232e5a110bd437186b5c6355a041750
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 10/5db3161abb311eef8c45bcf6565f4f378f785900ed3945acf740a9888c792f75b98ecb77f0775f3bf95502ff423529d23e94f41d80c8256e8fa05ed4b07cf471
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.1":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10/8ab897ca445968e0b96f6237641510f3243e59c180ee2ee8d83889c52ff735dd1bf3657fcd36db053e35e1d823dd53f2565d0b8021ea282c9fe62401c6c3bd6d
  languageName: node
  linkType: hard

"regl@npm:^1.3.11":
  version: 1.7.0
  resolution: "regl@npm:1.7.0"
  checksum: 10/085b9df406c1b2564999a13d2e323158b2b9d608b52b9b2ceae209e88885fde70d5059abf3ca2474290790d2b4058288321b7d323ef4dfdb557336252ae5515c
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10/a72468e2589270d91f06c7d36ec97a88db53ae5d6fe3787fadc943f0b0276b10347f89b363b2a82285f650bdcc135ad4a257c61bdd4d00d6df1fa24875b0ddaf
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: 10/839a3a890102a658f4cb3e7b2aa13a1f80a3a976b512020c3d1efc418491c48a886b6e481ea56afc6c4cb5eef678f23b2a4e70575e7534eccadf5e30ed2e56eb
  languageName: node
  linkType: hard

"resize-observer-polyfill@npm:1.5.x":
  version: 1.5.1
  resolution: "resize-observer-polyfill@npm:1.5.1"
  checksum: 10/e10ee50cd6cf558001de5c6fb03fee15debd011c2f694564b71f81742eef03fb30d6c2596d1d5bf946d9991cb692fcef529b7bd2e4057041377ecc9636c753ce
  languageName: node
  linkType: hard

"resolve@npm:^1.1.7":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/0a398b44da5c05e6e421d70108822c327675febb880eebe905587628de401854c61d5df02866ff34fc4cb1173a51c9f0e84a94702738df3611a62e2acdc68181
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.1.7#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/d4d878bfe3702d215ea23e75e0e9caf99468e3db76f5ca100d27ebdc527366fee3877e54bce7d47cc72ca8952fc2782a070d238bfa79a550eeb0082384c3b81a
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10/1f914879f97e7ee931ad05fe3afa629bd55270fc6cf1c1e589b6a99fab96d15daad0fa1a52a00c729ec0078045fe3e399bd4fd0c93bcc906957bdc17f89cb8e6
  languageName: node
  linkType: hard

"retry@npm:^0.13.1":
  version: 0.13.1
  resolution: "retry@npm:0.13.1"
  checksum: 10/6125ec2e06d6e47e9201539c887defba4e47f63471db304c59e4b82fc63c8e89ca06a77e9d34939a9a42a76f00774b2f46c0d4a4cbb3e287268bd018ed69426d
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: "npm:^10.3.7"
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 10/f3b8ce81eecbde4628b07bdf9e2fa8b684e0caea4999acb1e3b0402c695cd41f28cd075609a808e61ce2672f528ca079f675ab1d8e8d5f86d56643a03e0b8d2e
  languageName: node
  linkType: hard

"rrweb-cssom@npm:^0.8.0":
  version: 0.8.0
  resolution: "rrweb-cssom@npm:0.8.0"
  checksum: 10/07521ee36fb6569c17906afad1ac7ff8f099d49ade9249e190693ac36cdf27f88d9acf0cc66978935d5d0a23fca105643d7e9125b9a9d91ed9db9e02d31d7d80
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10/7eaf7a0cf37cc27b42fb3ef6a9b1df6e93a1c6d98c6c6702b02fe262d5fcbd89db63320793b99b21cb5348097d0a53de81bd5f4e8b86e20cc9412e3f1cfb4e83
  languageName: node
  linkType: hard

"saxes@npm:^6.0.0":
  version: 6.0.0
  resolution: "saxes@npm:6.0.0"
  dependencies:
    xmlchars: "npm:^2.2.0"
  checksum: 10/97b50daf6ca3a153e89842efa18a862e446248296622b7473c169c84c823ee8a16e4a43bac2f73f11fc8cb9168c73fbb0d73340f26552bac17970e9052367aa9
  languageName: node
  linkType: hard

"scheduler@npm:^0.26.0":
  version: 0.26.0
  resolution: "scheduler@npm:0.26.0"
  checksum: 10/1ecf2e5d7de1a7a132796834afe14a2d589ba7e437615bd8c06f3e0786a3ac3434655e67aac8755d9b14e05754c177e49c064261de2673aaa3c926bc98caa002
  languageName: node
  linkType: hard

"semver@npm:^7.3.5":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 10/36b1fbe1a2b6f873559cd57b238f1094a053dbfd997ceeb8757d79d1d2089c56d1321b9f1069ce263dc64cfa922fa1d2ad566b39426fe1ac6c723c1487589e10
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/505d62b8e088468917ca4e3f8f39d0e29f9a563b97dbebf92f4bd2c3172ccfb3c5b8e4566d5fcd00784a00433900e7cb8fbc404e2dbd8c3818ba05bb9d4a8a6d
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/c7614154a53ebf8c0428a6c40a3b0b47dac30587c1a19703d1b75f003803f73cdfa6a93474a9ba678fa565ef5fbddc2fae79bca03b7d22ab5fd5163dbe571a74
  languageName: node
  linkType: hard

"shallow-equal@npm:^3.0.0":
  version: 3.1.0
  resolution: "shallow-equal@npm:3.1.0"
  checksum: 10/2e0eda6ff74da859a3766fd1865522aacfbef945652d2b629fda0ca5cd9f1f6579e6b3da8bd21e6e8be3a8b49c24484cdd2b8a128f5793d48cff73a0edec15f9
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10/6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10/1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10/c9fa63bbbd7431066174a48ba2dd9986dfd930c3a8b59de9c29d7b6854ec1c12a80d15310869ea5166d413b99f041bfa3dd80a7947bcd44ea8e6eb3ffeabfa1f
  languageName: node
  linkType: hard

"slash@npm:^5.0.0":
  version: 5.1.0
  resolution: "slash@npm:5.1.0"
  checksum: 10/2c41ec6fb1414cd9bba0fa6b1dd00e8be739e3fe85d079c69d4b09ca5f2f86eafd18d9ce611c0c0f686428638a36c272a6ac14799146a8295f259c10cc45cde4
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10/927484aa0b1640fd9473cee3e0a0bcad6fce93fd7bbc18bac9ad0c33686f5d2e2c422fba24b5899c184524af01e11dd2bd051c2bf2b07e47aff8ca72cbfc60d2
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10/ee99e1dacab0985b52cbe5a75640be6e604135e9489ebdc3048635d186012fbaecc20fbbe04b177dee434c319ba20f09b3e7dfefb7d932466c0d707744eac05c
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10/ffcb622c22481dfcd7589aae71fbfd71ca34334064d181df64bf8b7feaeee19706aba4cffd1de35cc7bbaeeaa0af96be2d7f40fcbc7bc0ab69533a7ae9ffc4fb
  languageName: node
  linkType: hard

"sortablejs@npm:1.15.6":
  version: 1.15.6
  resolution: "sortablejs@npm:1.15.6"
  checksum: 10/3179071352662e6cff20d7d10792a934fc892a83a01ae09c7e604d2dd51daaf07283b00a8f2b13025f27959ff68a1469959bc94ef2a7049723d4c381a368a5ac
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.1, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10/ff9d8c8bf096d534a5b7707e0382ef827b4dd360a577d3f34d2b9f48e12c9d230b5747974ee7c607f0df65113732711bb701fe9ece3c7edbd43cb2294d707df3
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10/e7587128c423f7e43cc625fe2f87e6affdf5ca51c1cc468e910d8aaca46bb44a7fbcfa552f787b1d3987f7043aeb4527d1b99559e6621e01b42b3f45e5a24cbb
  languageName: node
  linkType: hard

"ssf@npm:~0.11.2":
  version: 0.11.2
  resolution: "ssf@npm:0.11.2"
  dependencies:
    frac: "npm:~1.1.2"
  checksum: 10/37f4b0f076e69a58554dbbe5d24fab412b7afc05ef48cdcb05b61db7e55b8a7002288047d95050571add4a50db30a772a26f04de23987300659154b199df8aef
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/7024c1a6e39b3f18aa8f1c8290e884fe91b0f9ca5a6c6d410544daad54de0ba664db879afe16412e187c6c292fd60b937f047ee44292e5c2af2dcc6d8e1a9b48
  languageName: node
  linkType: hard

"stargazer@workspace:.":
  version: 0.0.0-use.local
  resolution: "stargazer@workspace:."
  languageName: unknown
  linkType: soft

"state-machine-cat@npm:12.0.23":
  version: 12.0.23
  resolution: "state-machine-cat@npm:12.0.23"
  dependencies:
    "@hpcc-js/wasm-graphviz": "npm:1.7.0"
    ajv: "npm:8.17.1"
    fast-xml-parser: "npm:5.2.2"
    he: "npm:1.2.0"
    neotraverse: "npm:0.6.18"
  bin:
    smcat: dist/cli/main.mjs
    state-machine-cat: dist/cli/main.mjs
  checksum: 10/66ddcfd9008d9e6274362bbda0ce71919e9af62125e98101f1b01198821649646f5eaac40a48bd617460ba4664943557cfef1f40ca610953ed9f4e0be0e87b06
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10/e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10/ae3b5436d34fadeb6096367626ce987057713c566e1e7768818797e00ac5d62023d0f198c4e681eae9e20701721980b26a64a8f5b91238869592a9c6800719a2
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10/475f53e9c44375d6e72807284024ac5d668ee1d06010740dec0b9744f2ddf47de8d7151f80e5f6190fc8f384e802fdf9504b76a7e9020c9faee7103623338be2
  languageName: node
  linkType: hard

"strnum@npm:^2.1.0":
  version: 2.1.0
  resolution: "strnum@npm:2.1.0"
  checksum: 10/6237ecc0740816bb3088d89d8c17692f69b8c2a3d6bf25d6437ea40999d9afe28d1882496521d703804ce1ad3eeef71390073a6d39287ba1586d85ea360daa29
  languageName: node
  linkType: hard

"style-to-js@npm:1.1.16":
  version: 1.1.16
  resolution: "style-to-js@npm:1.1.16"
  dependencies:
    style-to-object: "npm:1.0.8"
  checksum: 10/a876cc49a29ac90c7723b4d6f002ac6c1ac5ccc6b5bc963d9c607cfc74b15927b704c9324df6f824f576c65689fe4b4ff79caabcd44a13d8a02641f721f1b316
  languageName: node
  linkType: hard

"style-to-object@npm:1.0.8":
  version: 1.0.8
  resolution: "style-to-object@npm:1.0.8"
  dependencies:
    inline-style-parser: "npm:0.2.4"
  checksum: 10/530b067325e3119bfaf75bdbe25cc86b02b559db00d881a74b98a2d5bb10ac953d1b455ed90c825963cf3b4bdaa1bda45f406d78d987391434b8d8ab3835df4e
  languageName: node
  linkType: hard

"stylehacks@npm:^7.0.5":
  version: 7.0.5
  resolution: "stylehacks@npm:7.0.5"
  dependencies:
    browserslist: "npm:^4.24.5"
    postcss-selector-parser: "npm:^7.1.0"
  peerDependencies:
    postcss: ^8.4.32
  checksum: 10/798ac0f92ff4489c251550d64b903f1aa8b5946e5b09b33ebf68290b5a345257cecf98c989526a5d462b560081194fead38c4f804ec016ceb8b1b3f17ec74fc5
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10/a9dc19ae2220c952bd2231d08ddeecb1b0328b61e72071ff4000c8384e145cc07c1c0bdb3b5a1cb06e186a7b2790f1dee793418b332f6ddf320de25d9125be7e
  languageName: node
  linkType: hard

"svgo@npm:^3.3.2":
  version: 3.3.2
  resolution: "svgo@npm:3.3.2"
  dependencies:
    "@trysound/sax": "npm:0.2.0"
    commander: "npm:^7.2.0"
    css-select: "npm:^5.1.0"
    css-tree: "npm:^2.3.1"
    css-what: "npm:^6.1.0"
    csso: "npm:^5.0.5"
    picocolors: "npm:^1.0.0"
  bin:
    svgo: ./bin/svgo
  checksum: 10/82fdea9b938884d808506104228e4d3af0050d643d5b46ff7abc903ff47a91bbf6561373394868aaf07a28f006c4057b8fbf14bbd666298abdd7cc590d4f7700
  languageName: node
  linkType: hard

"symbol-tree@npm:^3.2.4":
  version: 3.2.4
  resolution: "symbol-tree@npm:3.2.4"
  checksum: 10/c09a00aadf279d47d0c5c46ca3b6b2fbaeb45f0a184976d599637d412d3a70bbdc043ff33effe1206dea0e36e0ad226cb957112e7ce9a4bf2daedf7fa4f85c53
  languageName: node
  linkType: hard

"tabulator-tables@npm:6.3.1":
  version: 6.3.1
  resolution: "tabulator-tables@npm:6.3.1"
  checksum: 10/895b35947aff2427ba3a563f8ae2c5e31e9380787ba914dadb162711fddee6f967814745ab3c2dd585a2379b470ccbd57ce96e795199db69edf4296f217e0529
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10/12a2a4fc6dee23e07cc47f1aeb3a14a1afd3f16397e1350036a8f4cdfee8dcac7ef5978337a4e7b2ac2c27a9a6d46388fc2088ea7c80cb6878c814b1425f8ecf
  languageName: node
  linkType: hard

"thenby@npm:^1.3.4":
  version: 1.3.4
  resolution: "thenby@npm:1.3.4"
  checksum: 10/4422e71677adb8215a34e2096845f51114dd285faf730915e7171e6d2a778a29c6df75523c731eee3b7feddf57436ef7764ff526dbb1686af390a064ee15a14f
  languageName: node
  linkType: hard

"tiny-invariant@npm:^1.0.6":
  version: 1.3.3
  resolution: "tiny-invariant@npm:1.3.3"
  checksum: 10/5e185c8cc2266967984ce3b352a4e57cb89dad5a8abb0dea21468a6ecaa67cd5bb47a3b7a85d08041008644af4f667fb8b6575ba38ba5fb00b3b5068306e59fe
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12":
  version: 0.2.12
  resolution: "tinyglobby@npm:0.2.12"
  dependencies:
    fdir: "npm:^6.4.3"
    picomatch: "npm:^4.0.2"
  checksum: 10/4ad28701fa9118b32ef0e27f409e0a6c5741e8b02286d50425c1f6f71e6d6c6ded9dd5bbbbb714784b08623c4ec4d150151f1d3d996cfabe0495f908ab4f7002
  languageName: node
  linkType: hard

"tlds@npm:1.259.0":
  version: 1.259.0
  resolution: "tlds@npm:1.259.0"
  bin:
    tlds: bin.js
  checksum: 10/58e365baf5121d855bb11303dd1ebeec5df6233fbf2cf66ef951e6d8be2fbae225474c9dc64098046f4858ad7769274d1bbbf9736c407788636a064b80909c27
  languageName: node
  linkType: hard

"tldts-core@npm:^6.1.72":
  version: 6.1.72
  resolution: "tldts-core@npm:6.1.72"
  checksum: 10/0becdf18479c2241f569d4c766427246608c9e84f9090fafa930483ba6e514286138001943a0c48b25413a7c33e27a0a0bc58961b765e69b84d36e4bc8bcf0f2
  languageName: node
  linkType: hard

"tldts@npm:^6.1.32":
  version: 6.1.72
  resolution: "tldts@npm:6.1.72"
  dependencies:
    tldts-core: "npm:^6.1.72"
  bin:
    tldts: bin/cli.js
  checksum: 10/869373728bbe35fb8843c174fae2064930792092b9e40f1515f557376233dcffb6e8bfad1de6bd5f3e7dbdd6bf0913fabb55777e6e0bef50e21e820a3ccce9e0
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10/10dda13571e1f5ad37546827e9b6d4252d2e0bc176c24a101252153ef435d83696e2557fe128c4678e4e78f5f01e83711c703eef9814eb12dab028580d45980a
  languageName: node
  linkType: hard

"tough-cookie@npm:^5.1.1":
  version: 5.1.2
  resolution: "tough-cookie@npm:5.1.2"
  dependencies:
    tldts: "npm:^6.1.32"
  checksum: 10/de430e6e6d34b794137e05b8ac2aa6b74ebbe6cdceb4126f168cf1e76101162a4b2e0e7587c3b70e728bd8654fc39958b2035be7619ee6f08e7257610ba4cd04
  languageName: node
  linkType: hard

"tr46@npm:^5.0.0":
  version: 5.0.0
  resolution: "tr46@npm:5.0.0"
  dependencies:
    punycode: "npm:^2.3.1"
  checksum: 10/29155adb167d048d3c95d181f7cb5ac71948b4e8f3070ec455986e1f34634acae50ae02a3c8d448121c3afe35b76951cd46ed4c128fd80264280ca9502237a3e
  languageName: node
  linkType: hard

"tr46@npm:^5.1.0":
  version: 5.1.1
  resolution: "tr46@npm:5.1.1"
  dependencies:
    punycode: "npm:^2.3.1"
  checksum: 10/833a0e1044574da5790148fd17866d4ddaea89e022de50279967bcd6b28b4ce0d30d59eb3acf9702b60918975b3bad481400337e3a2e6326cffa5c77b874753d
  languageName: node
  linkType: hard

"tributejs@npm:5.1.3":
  version: 5.1.3
  resolution: "tributejs@npm:5.1.3"
  checksum: 10/7198ec8f7707caf4bd6e3f637acfdb743c4a3b23afa38653d3db030cd8a74f101d8bbc0cef65698d9c0f556f47b6f173d677defe16072599e6d78850021188ff
  languageName: node
  linkType: hard

"ts-custom-error@npm:^3.2.1":
  version: 3.3.1
  resolution: "ts-custom-error@npm:3.3.1"
  checksum: 10/92e3a2c426bf6049579aeb889b6f9787e0cfb6bb715a1457e2571708be7fe739662ca9eb2a8c61b72a2d32189645f4fbcf1a370087e030d922e9e2a7b7c1c994
  languageName: node
  linkType: hard

"tslib@npm:2.3.0":
  version: 2.3.0
  resolution: "tslib@npm:2.3.0"
  checksum: 10/9c55c9abd5ecd94e5fd37573db27e26933fb6c33506431403fb4627dcb8aa9a45b772afa6d05dd2c25dc1de22a213193251e6303742f3bcdc179e523a9295bfd
  languageName: node
  linkType: hard

"tslib@npm:^2.3.1, tslib@npm:^2.6.2":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10/3e2e043d5c2316461cb54e5c7fe02c30ef6dccb3384717ca22ae5c6b5bc95232a6241df19c622d9c73b809bea33b187f6dbc73030963e29950c2141bc32a79f7
  languageName: node
  linkType: hard

"tweetnacl@npm:1.0.3":
  version: 1.0.3
  resolution: "tweetnacl@npm:1.0.3"
  checksum: 10/ca122c2f86631f3c0f6d28efb44af2a301d4a557a62a3e2460286b08e97567b258c2212e4ad1cfa22bd6a57edcdc54ba76ebe946847450ab0999e6d48ccae332
  languageName: node
  linkType: hard

"uc.micro@npm:^2.0.0":
  version: 2.1.0
  resolution: "uc.micro@npm:2.1.0"
  checksum: 10/37197358242eb9afe367502d4638ac8c5838b78792ab218eafe48287b0ed28aaca268ec0392cc5729f6c90266744de32c06ae938549aee041fc93b0f9672d6b2
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10/6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10/beafdf3d6f44990e0a5ce560f8f881b4ee811be70b6ba0db25298c31c8cf525ed963572b48cd03be1c1349084f9e339be4241666d7cf1ebdad20598d3c652b27
  languageName: node
  linkType: hard

"universal-cookie@npm:8.0.1":
  version: 8.0.1
  resolution: "universal-cookie@npm:8.0.1"
  dependencies:
    cookie: "npm:^1.0.2"
  checksum: 10/25d4a13f34210e159753d014900358c3b413d75608c09be00ef6b6f8ab0e210612142d236a3a330f48f403cb8c2f3402988358c28fa7cae9431947b1c9944b12
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10/ecd8469fe0db28e7de9e5289d32bd1b6ba8f7183db34f3bfc4ca53c49891c2d6aa05f3fb3936a81285a905cc509fb641a0c3fc131ec786167eff41236ae32e60
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.1":
  version: 1.1.2
  resolution: "update-browserslist-db@npm:1.1.2"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10/e7bf8221dfb21eba4a770cd803df94625bb04f65a706aa94c567de9600fe4eb6133fda016ec471dad43b9e7959c1bffb6580b5e20a87808d2e8a13e3892699a9
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10/87af2776054ffb9194cf95e0201547d041f72ee44ce54b144da110e65ea7ca01379367407ba21de5c9edd52c74d95395366790de67f3eb4cc4afa0fe4424e76f
  languageName: node
  linkType: hard

"url-join@npm:5.0.0":
  version: 5.0.0
  resolution: "url-join@npm:5.0.0"
  checksum: 10/5921384a8ad4395b49ce4b50aa26efbc429cebe0bc8b3660ad693dd12fd859747b5369be0443e60e53a7850b2bc9d7d0687bcb94386662b40e743596bbf38101
  languageName: node
  linkType: hard

"use-memo-one@npm:^1.1.1":
  version: 1.1.3
  resolution: "use-memo-one@npm:1.1.3"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: 10/8f08eba26d69406b61bb4b8dacdd5a92bd6aef5b53d346dfe87954f7330ee10ecabc937cc7854635155d46053828e85c10b5a5aff7a04720e6a97b9f42999bac
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.2.0":
  version: 1.4.0
  resolution: "use-sync-external-store@npm:1.4.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10/08bf581a8a2effaefc355e9d18ed025d436230f4cc973db2f593166df357cf63e47b9097b6e5089b594758bde322e1737754ad64905e030d70f8ff7ee671fd01
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.2":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10/474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"utility-types@npm:^3.10.0":
  version: 3.11.0
  resolution: "utility-types@npm:3.11.0"
  checksum: 10/a3c51463fc807ed04ccc8b5d0fa6e31f3dcd7a4cbd30ab4bc6d760ce5319dd493d95bf04244693daf316f97e9ab2a37741edfed8748ad38572a595398ad0fdaf
  languageName: node
  linkType: hard

"w3c-xmlserializer@npm:^5.0.0":
  version: 5.0.0
  resolution: "w3c-xmlserializer@npm:5.0.0"
  dependencies:
    xml-name-validator: "npm:^5.0.0"
  checksum: 10/d78f59e6b4f924aa53b6dfc56949959229cae7fe05ea9374eb38d11edcec01398b7f5d7a12576bd5acc57ff446abb5c9115cd83b9d882555015437cf858d42f0
  languageName: node
  linkType: hard

"webidl-conversions@npm:^7.0.0":
  version: 7.0.0
  resolution: "webidl-conversions@npm:7.0.0"
  checksum: 10/4c4f65472c010eddbe648c11b977d048dd96956a625f7f8b9d64e1b30c3c1f23ea1acfd654648426ce5c743c2108a5a757c0592f02902cf7367adb7d14e67721
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^3.1.1":
  version: 3.1.1
  resolution: "whatwg-encoding@npm:3.1.1"
  dependencies:
    iconv-lite: "npm:0.6.3"
  checksum: 10/bbef815eb67f91487c7f2ef96329743f5fd8357d7d62b1119237d25d41c7e452dff8197235b2d3c031365a17f61d3bb73ca49d0ed1582475aa4a670815e79534
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^4.0.0":
  version: 4.0.0
  resolution: "whatwg-mimetype@npm:4.0.0"
  checksum: 10/894a618e2d90bf444b6f309f3ceb6e58cf21b2beaa00c8b333696958c4076f0c7b30b9d33413c9ffff7c5832a0a0c8569e5bb347ef44beded72aeefd0acd62e8
  languageName: node
  linkType: hard

"whatwg-url@npm:^14.0.0":
  version: 14.1.0
  resolution: "whatwg-url@npm:14.1.0"
  dependencies:
    tr46: "npm:^5.0.0"
    webidl-conversions: "npm:^7.0.0"
  checksum: 10/3afd325de6cf3a367820ce7c3566a1f78eb1409c4f27b1867c74c76dab096d26acedf49a8b9b71db53df7d806ec2e9ae9ed96990b2f7d1abe6ecf1fe753af6eb
  languageName: node
  linkType: hard

"whatwg-url@npm:^14.1.1":
  version: 14.2.0
  resolution: "whatwg-url@npm:14.2.0"
  dependencies:
    tr46: "npm:^5.1.0"
    webidl-conversions: "npm:^7.0.0"
  checksum: 10/f0a95b0601c64f417c471536a2d828b4c16fe37c13662483a32f02f183ed0f441616609b0663fb791e524e8cd56d9a86dd7366b1fc5356048ccb09b576495e7c
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10/4782f8a1d6b8fc12c65e968fea49f59752bf6302dc43036c3bf87da718a80710f61a062516e9764c70008b487929a73546125570acea95c5b5dcc8ac3052c70f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10/6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"wildcard@npm:^1.1.0":
  version: 1.1.2
  resolution: "wildcard@npm:1.1.2"
  checksum: 10/f93bf48a23b7b776f7960fa7f252af55da265b4ce8127852e420f04a907b78073bc0412f74fc662f561667f3277473974f6553a260ece67f53b1975d128320ab
  languageName: node
  linkType: hard

"wmf@npm:~1.0.1":
  version: 1.0.2
  resolution: "wmf@npm:1.0.2"
  checksum: 10/b82d3d95403d6f072ad734ade57779f10be9139ba7054b3b97707759c5b01bd5b677134fde4c5f08ffa26bc0d89153273c42359bb9a3d0f18d9a079a113f7990
  languageName: node
  linkType: hard

"word@npm:~0.3.0":
  version: 0.3.0
  resolution: "word@npm:0.3.0"
  checksum: 10/e28a233aca36f9897f08d6db1a881bbe4ceae9e2da6753e09f5fdaae4e8d04149f5c4d9c74dc277eeef0dc043e459b11d122e294b9ea0f2bd3f8310f0b117964
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/cebdaeca3a6880da410f75209e68cd05428580de5ad24535f22696d7d9cab134d1f8498599f344c3cf0fb37c1715807a183778d8c648d6cc0cb5ff2bb4236540
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7b1e4b35e9bb2312d2ee9ee7dc95b8cb5f8b4b5a89f7dde5543fe66c1e3715663094defa50d75454ac900bd210f702d575f15f3f17fa9ec0291806d2578d1ddf
  languageName: node
  linkType: hard

"ws@npm:^8.18.0":
  version: 8.18.0
  resolution: "ws@npm:8.18.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10/70dfe53f23ff4368d46e4c0b1d4ca734db2c4149c6f68bc62cb16fc21f753c47b35fcc6e582f3bdfba0eaeb1c488cddab3c2255755a5c3eecb251431e42b3ff6
  languageName: node
  linkType: hard

"xlsx@npm:0.18.5":
  version: 0.18.5
  resolution: "xlsx@npm:0.18.5"
  dependencies:
    adler-32: "npm:~1.3.0"
    cfb: "npm:~1.2.1"
    codepage: "npm:~1.15.0"
    crc-32: "npm:~1.2.1"
    ssf: "npm:~0.11.2"
    wmf: "npm:~1.0.1"
    word: "npm:~0.3.0"
  bin:
    xlsx: bin/xlsx.njs
  checksum: 10/402df8d1e2678d57ad942332d78049c629695145478c7519800f1d6017c1ddd001417586d9c9cca7d55a43af68e47b77f6eca0399a77093b6ea1f131352f92cb
  languageName: node
  linkType: hard

"xml-name-validator@npm:^5.0.0":
  version: 5.0.0
  resolution: "xml-name-validator@npm:5.0.0"
  checksum: 10/43f30f3f6786e406dd665acf08cd742d5f8a46486bd72517edb04b27d1bcd1599664c2a4a99fc3f1e56a3194bff588b12f178b7972bc45c8047bdc4c3ac8d4a1
  languageName: node
  linkType: hard

"xmlchars@npm:^2.2.0":
  version: 2.2.0
  resolution: "xmlchars@npm:2.2.0"
  checksum: 10/4ad5924974efd004a47cce6acf5c0269aee0e62f9a805a426db3337af7bcbd331099df174b024ace4fb18971b8a56de386d2e73a1c4b020e3abd63a4a9b917f1
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10/5f1b5f95e3775de4514edbb142398a2c37849ccfaf04a015be5d75521e9629d3be29bd4432d23c57f37e5b61ade592fb0197022e9993f81a06a5afbdcda9346d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10/4cb02b42b8a93b5cf50caf5d8e9beb409400a8a4d85e83bb0685c1457e9ac0b7a00819e9f5991ac25ffabb56a78e2f017c1acc010b3a1babfe6de690ba531abd
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10/1884d272d485845ad04759a255c71775db0fac56308764b4c77ea56a20d56679fad340213054c8c9c9c26fcfd4c4b2a90df993b7e0aaf3cdb73c618d1d1a802a
  languageName: node
  linkType: hard

"yaml@npm:^2.4.2":
  version: 2.7.0
  resolution: "yaml@npm:2.7.0"
  bin:
    yaml: bin.mjs
  checksum: 10/c8c314c62fbd49244a6a51b06482f6d495b37ab10fa685fcafa1bbaae7841b7233ee7d12cab087bcca5a0b28adc92868b6e437322276430c28d00f1c1732eeec
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10/9dc2c217ea3bf8d858041252d43e074f7166b53f3d010a8c711275e09cd3d62a002969a39858b92bbda2a6a63a585c7127014534a560b9c69ed2d923d113406e
  languageName: node
  linkType: hard

"yargs@npm:^17.0.0":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10/abb3e37678d6e38ea85485ed86ebe0d1e3464c640d7d9069805ea0da12f69d5a32df8e5625e370f9c96dd1c2dc088ab2d0a4dd32af18222ef3c4224a19471576
  languageName: node
  linkType: hard

"zrender@npm:5.6.1":
  version: 5.6.1
  resolution: "zrender@npm:5.6.1"
  dependencies:
    tslib: "npm:2.3.0"
  checksum: 10/25dfd476be243f051614f131675855d184eb05e8b9a39dc41146a3a553a17aad5ceba77e166d525be9c4adc2bb237c56dfdfb3456667940bbeddb05eef3deac7
  languageName: node
  linkType: hard
