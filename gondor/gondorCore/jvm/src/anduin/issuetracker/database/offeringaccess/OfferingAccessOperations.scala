// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.issuetracker.database.offeringaccess

import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{RecordTask, RecordIO}
import anduin.id.offering.GlobalOfferingId
import anduin.issuetracker.database.offeringaccess.OfferingAccessStoreProvider.offeringAccessMessageKeyMapping
import anduin.protobuf.issuetracker.internal.offeringaccess.OfferingAccessMessage

final case class OfferingAccessOperations(store: OfferingAccessStoreProvider.Store) {

  def getOpt(offeringId: GlobalOfferingId): RecordTask[Option[OfferingAccessMessage]] = {
    store.getOpt(offeringId)
  }

  def add(record: OfferingAccessMessage): RecordTask[Unit] = {
    store.create(record).map(_ => ())
  }

  def update(
    offeringId: GlobalOfferingId,
    updateFunc: OfferingAccessMessage => OfferingAccessMessage
  ): RecordTask[Option[OfferingAccessMessage]] = {
    getOpt(offeringId).flatMap {
      _.fold[RecordTask[Option[OfferingAccessMessage]]](RecordIO.succeed(None)) { oldMsg =>
        store.update(updateFunc(oldMsg)).map { fdbStoredRecord =>
          offeringAccessMessageKeyMapping.recordModel.fromJavaMessage(fdbStoredRecord.getRecord).toOption
        }
      }
    }
  }

  def updateOrAddOfferingAccess(
    offeringId: GlobalOfferingId,
    defaultModel: OfferingAccessMessage,
    updateFunc: OfferingAccessMessage => OfferingAccessMessage
  ): RecordTask[Unit] = {
    for {
      oldMsgOpt <- getOpt(offeringId)
      _ <- oldMsgOpt.fold(
        add(defaultModel)
      ) { oldMsg =>
        store.update(updateFunc(oldMsg)).map(_ => ())
      }
    } yield ()
  }

  def idExisted(offeringId: GlobalOfferingId): RecordTask[Boolean] = {
    store.exist(offeringId)
  }

}

object OfferingAccessOperations
    extends FDBOperations.Single[OfferingAccessStoreProvider.RecordEnum, OfferingAccessOperations](
      OfferingAccessStoreProvider
    )
