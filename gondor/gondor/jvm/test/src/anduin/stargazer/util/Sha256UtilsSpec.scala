// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.stargazer.util

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import anduin.test.JvmTaskMatchers
import anduin.utils.stream.ZStreamIOUtils
import com.anduin.stargazer.UnitSpec

final class Sha256UtilsSpec extends UnitSpec with JvmTaskMatchers {

  override protected def taskTimeout: FiniteDuration = FiniteDuration(30, TimeUnit.SECONDS)

  "SHA256Utils" in {
    Sha256Utils.getHash("anduin".getBytes) shouldBe "80520b940c07cabdf5f033fd771e0344d668eef7ce90986ba8cbb1303791e4cd"
    // hash start with 0
    Sha256Utils.getHash("Rotvrv".getBytes) shouldBe "0e6638301fd920814f653b76d2b6b05fc15017b74f9c1830a780bcf97ce55a42"
  }

  "SHA256Utils for ZStream" in {
    Sha256Utils.getHash(ZStreamIOUtils.fromByteArray("anduin".getBytes)) shouldBe
      succeedWith("80520b940c07cabdf5f033fd771e0344d668eef7ce90986ba8cbb1303791e4cd")
    // hash start with 0
    Sha256Utils.getHash(ZStreamIOUtils.fromByteArray("Rotvrv".getBytes)) shouldBe
      succeedWith("0e6638301fd920814f653b76d2b6b05fc15017b74f9c1830a780bcf97ce55a42")
  }
}
