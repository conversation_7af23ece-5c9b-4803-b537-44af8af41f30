import {
  Context,
  DeployStep,
  ModuleGraph,
  Plan,
  Planner,
} from "@anduintransaction/rivendell";
import { loadContextFromCli } from "@contextes";
import { loadModules } from "@modules/index";

function filterDeploy(
  plan: Plan,
  fn: (s: DeployStep) => boolean,
  inverse: boolean = false,
) {
  return plan.filter((s) => {
    if (s.type !== "deploy") return false;
    let check = fn(s);
    if (inverse) {
      check = !check;
    }
    return check;
  });
}

// @ts-ignore
function filterDeployByKind(
  plan: Plan,
  kind: string,
  inverse: boolean = false,
): Plan {
  return filterDeploy(
    plan,
    (s) => s.object.kind.toLowerCase() === kind.toLowerCase(),
    inverse,
  );
}

// @ts-ignore
function filterDeployByName(
  plan: Plan,
  name: string,
  inverse: boolean = false,
): Plan {
  return filterDeploy(
    plan,
    (s) => s.object.metadata!.name!.toLowerCase() === name.toLowerCase(),
    inverse,
  );
}

// @ts-ignore
async function debugGraph(ctx: Context) {
  process.env["GONDOR_UPGRADE_ONLY"] = "true";
  const modules = await loadModules(ctx);
  const graph = ModuleGraph.resolve(...modules);
  graph.printGraphViz();
  const planner = new Planner(ctx);
  const plan = await planner.planFromGraph(graph);
  Planner.show(plan);
}

// @ts-ignore
async function debugModules(ctx: Context) {
  let modules = await loadModules(ctx);
  modules = modules
    .filter((m) => ["appconfig"].includes(m.name))
    .map((m) => m.cloneGeneratorOnly());
  const graph = ModuleGraph.resolve(...modules);
  const planner = new Planner(ctx);
  const plan = await planner.planFromGraph(graph);

  Planner.showManifests(plan);
}

async function main() {
  const ctx = await loadContextFromCli(process.argv.slice(2), false);
  console.log("printing");
  await debugModules(ctx);
}

main();
