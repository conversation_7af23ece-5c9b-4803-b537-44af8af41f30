// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service

import zio.Task

import anduin.dataroom.flow.DataRoomValidateOperations.PlanCheck
import anduin.dataroom.validator.DataRoomValidator
import anduin.dms.DmsFeature
import anduin.environment.EnvironmentCheck
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FileId, FolderId}
import anduin.rag.{RagIndexService, RagSearchService, SearchIndexState}
import anduin.stargazer.service.dataroom.{SemanticSearchDataRoomParams, SemanticSearchDataRoomResponse}
import com.anduin.stargazer.endpoints.{FileKeywordSearchResult, FolderKeywordSearchResult}
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.utils.ZIOUtils

final case class DataRoomSemanticSearchService(
  backendConfig: GondorBackendConfig,
  dataRoomPermissionService: DataRoomPermissionService,
  dataRoomValidator: DataRoomValidator,
  ragIndexService: RagIndexService,
  ragSearchService: RagSearchService
) {
  private val config = backendConfig.dataRoomSemanticSearchConfig
  val isEnabled: Boolean = config.isEnabled

  private def isShowSearchEnabled(dataRoomWorkflowId: DataRoomWorkflowId, actor: UserId): Task[Boolean] = {
    DataRoomServiceUtils
      .getDataRoomState(
        actor = actor,
        dataRoomWorkflowId = dataRoomWorkflowId,
        isArchived = false,
        includeInvited = false,
        includeToaNotAccepted = false,
        includeGroup = false,
        planCheck = PlanCheck.RequirePlan(Set()),
        environmentCheck = EnvironmentCheck.Bypass
      )
      .map(_.createdState.showSearch)
  }

  def isDataRoomIndexed(
    dataRoomWorkflowId: DataRoomWorkflowId
  ): Task[Boolean] = {
    ragIndexService
      .getChannelIndexState(dataRoomWorkflowId)(
        using DmsFeature.DataRoom
      )
      .map(state =>
        isEnabled
          && state != SearchIndexState.NotIndexed
          && state != SearchIndexState.Indexing
          && state != SearchIndexState.Error
      )
  }

  def indexFolders(
    dataRoomWorkflowId: DataRoomWorkflowId,
    folderIds: Seq[FolderId],
    actor: UserId,
    withTags: Option[Seq[String]] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      showSearch <- isShowSearchEnabled(dataRoomWorkflowId, actor)
      _ <- ZIOUtils.when(isEnabled && showSearch) {
        ragIndexService.batchSyncFolderIndex(folderIds, actor, withTags)
      }
    } yield ()
  }

  def deleteFolderIndexes(
    folderIds: Seq[FolderId],
    actor: UserId,
    isPermanent: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    ragIndexService.batchDeleteFolderIndexes(folderIds, actor, isPermanent)
  }

  def startIndexFiles(
    dataRoomWorkflowId: DataRoomWorkflowId,
    fileIds: Seq[FileId],
    actor: UserId,
    withTags: Option[Seq[String]] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      showSearch <- isShowSearchEnabled(dataRoomWorkflowId, actor)
      _ <- ZIOUtils.when(isEnabled && showSearch) {
        ragIndexService.batchSyncFileIndex(fileIds, actor, withTags)
      }
    } yield ()
  }

  def deleteFileIndexes(
    fileIds: Seq[FileId],
    actor: UserId,
    isPermanent: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    ragIndexService.batchDeleteFileIndexes(fileIds, actor, isPermanent)
  }

  def startUpdateSearchIndexWorkflow(
    dataRoomWorkflowId: DataRoomWorkflowId,
    actor: UserId
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      showSearch <- isShowSearchEnabled(dataRoomWorkflowId, actor)
      _ <- ZIOUtils.when(isEnabled && showSearch) {
        ragIndexService.startUpdateIndexWorkflow(dataRoomWorkflowId, actor)
      }
    } yield ()
  }

  private def keywordSearchFolderUnsafe(
    dataRoomWorkflowId: DataRoomWorkflowId,
    query: String,
    actor: UserId,
    limit: Int = 10
  ): Task[List[FolderKeywordSearchResult]] = {
    ragSearchService.keywordSearchFolder(actor, dataRoomWorkflowId, query, limit)(
      using DmsFeature.DataRoom
    )
  }

  private def keywordSearchFileUnsafe(
    dataRoomWorkflowId: DataRoomWorkflowId,
    query: String,
    actor: UserId,
    limit: Int = 10
  ): Task[List[FileKeywordSearchResult]] = {
    ragSearchService.keywordSearchFile(actor, dataRoomWorkflowId, query, limit)(
      using DmsFeature.DataRoom
    )
  }

  def keywordSearchUnsafe(
    params: SemanticSearchDataRoomParams,
    actor: UserId
  ): Task[SemanticSearchDataRoomResponse] = {
    if (params.isFileSearch) {
      keywordSearchFileUnsafe(
        params.dataRoomWorkflowId,
        params.query,
        actor,
        params.limit
      ).map(SemanticSearchDataRoomResponse(_))
    } else {
      keywordSearchFolderUnsafe(
        params.dataRoomWorkflowId,
        params.query,
        actor,
        params.limit
      ).map(SemanticSearchDataRoomResponse(_))
    }
  }

  def keywordSearch(
    params: SemanticSearchDataRoomParams,
    actor: UserId
  ): Task[SemanticSearchDataRoomResponse] = {
    dataRoomValidator.checkJoinedUser(params, actor) *> keywordSearchUnsafe(params, actor)
  }

}
