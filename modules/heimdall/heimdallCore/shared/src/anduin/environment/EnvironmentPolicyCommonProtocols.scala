// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.environment

import io.circe.Codec
import anduin.circe.generic.semiauto.{deriveCodecWithDefaults, deriveCodecWithDefaultsAndTypename}
import anduin.id.account.EnterpriseLoginConfigId
import anduin.id.environment.{EnvironmentId, EnvironmentSSOBindingId, GlobalEmailDomainEnvironmentSSOBindingId}
import anduin.id.offering.GlobalOfferingId
import anduin.protobuf.environment.EnvironmentPolicyReauthenticationFallbackPolicy
import anduin.model.codec.ProtoCodecs.given
import anduin.utils.ScalaUtils

object EnvironmentPolicyCommonProtocols {

  sealed trait PolicyBinding derives CanEqual

  object PolicyBinding {
    given Codec.AsObject[PolicyBinding] = deriveCodecWithDefaultsAndTypename

    case object FallbackPolicy extends PolicyBinding
    case object AnduinAuthentication extends PolicyBinding
    final case class SomeSSOConfig(enterpriseLoginConfigId: EnterpriseLoginConfigId) extends PolicyBinding

    given Codec.AsObject[FallbackPolicy.type] = deriveCodecWithDefaults
    given Codec.AsObject[AnduinAuthentication.type] = deriveCodecWithDefaults
    given Codec.AsObject[SomeSSOConfig] = deriveCodecWithDefaults
  }

  final case class SSOConfig(
    configId: EnterpriseLoginConfigId,
    providerName: String,
    providerLogoUrl: Option[String]
  )

  object SSOConfig {
    given Codec.AsObject[SSOConfig] = deriveCodecWithDefaults
  }

  sealed trait EnvironmentAuthenticationPolicy derives CanEqual

  object EnvironmentAuthenticationPolicy {

    given Codec.AsObject[EnvironmentAuthenticationPolicy] = deriveCodecWithDefaultsAndTypename

    case object Fallback extends EnvironmentAuthenticationPolicy
    case object AnduinAuthentication extends EnvironmentAuthenticationPolicy
    final case class UseProvider(ssoConfig: SSOConfig) extends EnvironmentAuthenticationPolicy

    given Codec.AsObject[Fallback.type] = deriveCodecWithDefaults
    given Codec.AsObject[AnduinAuthentication.type] = deriveCodecWithDefaults
    given Codec.AsObject[UseProvider] = deriveCodecWithDefaults

  }

  final case class EnvironmentAuthenticationDecision(
    environmentId: EnvironmentId,
    offeringId: GlobalOfferingId,
    decision: EnvironmentAuthenticationDecision.Decision
  )

  object EnvironmentAuthenticationDecision {

    given Codec.AsObject[EnvironmentAuthenticationDecision] = deriveCodecWithDefaults

    sealed trait Decision derives CanEqual {
      def isEmailBound: Boolean
      def isUserBound: Boolean
      def isFallback: Boolean
    }

    object Decision {
      given Codec.AsObject[Decision] = deriveCodecWithDefaultsAndTypename

      final case class EmailBound(
        domain: String,
        bindingId: GlobalEmailDomainEnvironmentSSOBindingId,
        policy: EnvironmentAuthenticationPolicy
      ) extends Decision {
        override def isEmailBound: Boolean = true
        override def isUserBound: Boolean = false
        override def isFallback: Boolean = false
      }

      given Codec.AsObject[EmailBound] = deriveCodecWithDefaults

      final case class UserBound(
        bindingId: EnvironmentSSOBindingId,
        policy: EnvironmentAuthenticationPolicy,
        fallback: Fallback
      ) extends Decision {
        override def isEmailBound: Boolean = false
        override def isUserBound: Boolean = true
        override def isFallback: Boolean = false
      }

      given Codec.AsObject[UserBound] = deriveCodecWithDefaults

      sealed trait FallbackMethod derives CanEqual

      object FallbackMethod {
        given Codec.AsObject[FallbackMethod] = deriveCodecWithDefaultsAndTypename

        final case class Blocked(title: String, body: String) extends FallbackMethod
        case object AnduinAuthentication extends FallbackMethod
        final case class ListProviders(ssoConfigs: Seq[SSOConfig]) extends FallbackMethod
        final case class UseDefaultProvider(ssoConfig: SSOConfig) extends FallbackMethod

        given Codec.AsObject[Blocked] = deriveCodecWithDefaults
        given Codec.AsObject[AnduinAuthentication.type] = deriveCodecWithDefaults
        given Codec.AsObject[ListProviders] = deriveCodecWithDefaults
        given Codec.AsObject[UseDefaultProvider] = deriveCodecWithDefaults
      }

      final case class Fallback(fallbackMethod: FallbackMethod) extends Decision {
        override def isEmailBound: Boolean = false
        override def isUserBound: Boolean = false
        override def isFallback: Boolean = true
      }

      given Codec.AsObject[Fallback] = deriveCodecWithDefaults

    }

  }

  final case class EnvironmentAuthenticationAction(
    environmentId: EnvironmentId,
    actionType: EnvironmentAuthenticationAction.ActionType,
    actionMethod: EnvironmentAuthenticationAction.ActionMethod,
    actualActionMethod: Seq[
      EnvironmentAuthenticationAction.ActionMethod
    ], // The origin action method the user login from
    offeringId: GlobalOfferingId
  ) derives CanEqual {

    def compatibleWith(other: EnvironmentAuthenticationAction): Boolean = {
      environmentId == other.environmentId && offeringId == other.offeringId && (actionType.isOverride || other.actionType.isOverride || actionMethod == other.actionMethod)
    }

  }

  object EnvironmentAuthenticationAction {
    given Codec.AsObject[EnvironmentAuthenticationAction] = deriveCodecWithDefaults

    sealed trait ActionType derives CanEqual {
      def compatibleWith(other: ActionType): Boolean

      def isOverride: Boolean
    }

    object ActionType {
      given Codec.AsObject[ActionType] = deriveCodecWithDefaultsAndTypename

      final case class Fallback(fallbackPolicy: EnvironmentPolicyReauthenticationFallbackPolicy) extends ActionType {

        override def compatibleWith(other: ActionType): Boolean = {
          ScalaUtils
            .downcastOpt[ActionType, Fallback](other)
            .fold(false)(otherFallback => fallbackPolicy == otherFallback.fallbackPolicy)
        }

        override def isOverride: Boolean = false

      }

      final case class UserBound(
        bindingId: EnvironmentSSOBindingId
      ) extends ActionType {

        override def compatibleWith(other: ActionType): Boolean = {
          ScalaUtils.downcastOpt[ActionType, UserBound](other).nonEmpty
        }

        override def isOverride: Boolean = false

      }

      final case class EmailBound(
        domain: String,
        bindingId: GlobalEmailDomainEnvironmentSSOBindingId
      ) extends ActionType {

        override def compatibleWith(other: ActionType): Boolean = {
          ScalaUtils.downcastOpt[ActionType, EmailBound](other).fold(false)(_.domain == domain)
        }

        override def isOverride: Boolean = false

      }

      case object NoAction extends ActionType {
        override def compatibleWith(other: ActionType): Boolean = true
        override def isOverride: Boolean = false
      }

      final case class Override(startTime: Long) extends ActionType {
        override def compatibleWith(other: ActionType): Boolean = true
        override def isOverride: Boolean = true
      }

      given Codec.AsObject[Fallback] = deriveCodecWithDefaults
      given Codec.AsObject[UserBound] = deriveCodecWithDefaults
      given Codec.AsObject[EmailBound] = deriveCodecWithDefaults
      given Codec.AsObject[NoAction.type] = deriveCodecWithDefaults
      given Codec.AsObject[Override] = deriveCodecWithDefaults
    }

    sealed trait ActionMethod derives CanEqual {
      def enterpriseLoginConfigIdOpt: Option[EnterpriseLoginConfigId]
    }

    object ActionMethod {
      given Codec.AsObject[ActionMethod] = deriveCodecWithDefaultsAndTypename

      final case class AnduinAuthentication(enterpriseLoginConfigId: Option[EnterpriseLoginConfigId])
          extends ActionMethod {
        override def enterpriseLoginConfigIdOpt: Option[EnterpriseLoginConfigId] = enterpriseLoginConfigId
      }

      final case class UseProvider(enterpriseLoginConfigId: EnterpriseLoginConfigId) extends ActionMethod {
        override def enterpriseLoginConfigIdOpt: Option[EnterpriseLoginConfigId] = Some(enterpriseLoginConfigId)
      }

      given Codec.AsObject[AnduinAuthentication] = deriveCodecWithDefaults
      given Codec.AsObject[UseProvider] = deriveCodecWithDefaults
    }

  }

}
