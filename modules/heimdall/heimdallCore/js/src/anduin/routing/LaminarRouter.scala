// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.routing

import scala.util.{Failure, Success, Try}

import com.raquo.airstream.state.StrictSignal
import com.raquo.laminar.api.L.*
import com.raquo.waypoint.{Route, Router}
import io.circe.Json
import org.scalajs.dom

import stargazer.model.routing.{Page, PageInstances, StaticAuthPage}

final case class LaminarRouter(
  routes: List[Route[? <: Page, ?]]
) {

  private val router = LaminarRouter.getRouter(routes)

  val currentPageSignal: StrictSignal[Page] = router.currentPageSignal

  def absoluteUrlForPage(page: Page): String = {
    router.absoluteUrlForPage(page)
  }

  // Navigation helpers

  private def nextPageWithCurrent(toPage: => Option[Page] => Page): Page = {
    toPage(currentPageSignal.tryNow().toOption)
  }

  def refreshToPage(toPage: => Option[Page] => Page): Unit = {
    val nextPage = nextPageWithCurrent(toPage)
    dom.window.location.href = router.relativeUrlForPage(nextPage)
  }

  def pushState(page: Page): Unit = {
    router.pushState(page)
  }

  def pushStateToPage(toPage: => Option[Page] => Page): Unit = {
    val nextPage = nextPageWithCurrent(toPage)
    router.pushState(nextPage)
  }

  def navigateTo(page: Page, openNewTab: Boolean = false): Binder[HtmlElement] = Binder { el =>
    val isLinkElement = el.ref.isInstanceOf[dom.html.Anchor] // scalafix:ok DisableSyntax.isInstanceOf

    if (isLinkElement) {
      Try(router.absoluteUrlForPage(page)) match {
        case Success(url) =>
          el.amend(
            href(url),
            when(openNewTab) {
              Seq(
                target := "_blank",
                rel := "noreferrer noopener"
              )
            }
          )
        case Failure(err) =>
          dom.console.error(err)
      }
    }

    // If element is a link and user is holding a modifier while clicking or openNewTab is true:
    //  - Do nothing, browser will open the URL in new tab / window / etc. depending on the modifier key
    // Otherwise:
    //  - Perform regular pushState transition
    (onClick.filter { ev =>
      !(isLinkElement && (ev.ctrlKey || ev.metaKey || ev.shiftKey || ev.altKey || openNewTab))
    }.preventDefault --> (_ => router.pushState(page))).bind(el)
  }

}

object LaminarRouter {

  private def getRouter(routes: List[Route[? <: Page, ?]]): Router[Page] = {
    new Router[Page](
      routes = routes,
      getPageTitle = _ => "",
      serializePage = page => PageInstances.encoder(page).toString,
      deserializePage = pageStr =>
        PageInstances.decoder.decodeJson(Json.fromString(pageStr)).getOrElse(StaticAuthPage.Dashboard),
      popStateEvents = windowEvents(_.onPopState),
      origin = dom.window.location.href.takeWhile(c => c != '#').dropRight(1),
      owner = unsafeWindowOwner
    )
  }

}
