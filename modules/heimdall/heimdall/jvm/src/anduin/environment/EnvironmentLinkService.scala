// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.environment

import java.net.URI

import zio.{Task, ZIO}

import anduin.customdomain.CustomDomainService
import anduin.environment.EnvironmentProtocols.{
  GetEnvironmentRedirectBaseUrlParams,
  GetEnvironmentRedirectBaseUrlResponse
}
import anduin.id.environment.EnvironmentId
import anduin.id.offering.OfferingId
import anduin.service.PublicRequestContext
import com.anduin.stargazer.service.GondorConfig
import com.anduin.stargazer.service.utils.ZIOUtils

final case class EnvironmentLinkService(
  gondorConfig: GondorConfig,
  customDomainService: CustomDomainService,
  environmentService: EnvironmentService,
  environmentAuthenticationIntegrationService: EnvironmentAuthenticationIntegrationService
) {

  def getEnvironmentRedirectUrl(
    params: GetEnvironmentRedirectBaseUrlParams,
    ctx: PublicRequestContext
  ): Task[GetEnvironmentRedirectBaseUrlResponse] = {
    for {
      _ <- ZIO.logInfo(s"Get redirect url for engagement ${params.engagementIdOpt}")
      environmentIdOpt <- ZIOUtils.traverseOption2(params.engagementIdOpt)(
        environmentAuthenticationIntegrationService.resolveEnvironmentByEngagement
      )
      resp <- getEnvironmentRedirectBaseUrl(
        params.currentUrl,
        environmentIdOpt,
        params.offeringId,
        ctx
      )
    } yield resp
  }

  private def getEnvironmentRedirectBaseUrl(
    currentUrl: String,
    environmentIdOpt: Option[EnvironmentId],
    offeringId: OfferingId,
    ctx: PublicRequestContext
  ): Task[GetEnvironmentRedirectBaseUrlResponse] = {
    for {
      currentEnvironmentOpt <- environmentService.getCurrentEnvironmentWithCustomDomain(ctx)
      currentDomain = currentEnvironmentOpt.map(_._3.domain).getOrElse(gondorConfig.backendConfig.server.baseUrl)

      offeringEnvironmentCustomDomains <- ZIOUtils
        .traverseOption(environmentIdOpt)(
          environmentService.getCustomDomainsForEnvironment
        )
        .map(_.getOrElse(Seq.empty))

      offeringEnvironmentBaseUrlWithPrefixPath <- environmentIdOpt.fold(
        customDomainService.resolveOfferingCustomDomainPath(offeringId, "")
      )(
        environmentService.resolvePath(_, Some(offeringId), "")
      )

      shouldNotRedirect = {
        val offeringEnvironmentAcceptableCustomDomains = offeringEnvironmentCustomDomains.filter {
          case (envDomain, _) =>
            val isAcceptableOfferingDomain =
              envDomain.offeringId.flatMap(_.toOfferingId).contains(offeringId) && !envDomain.category.isTertiary
            val isAcceptablePlatformDomain =
              envDomain.domainType.isPlatform && !envDomain.category.isTertiary
            isAcceptableOfferingDomain || isAcceptablePlatformDomain
        }
        val isCurrentDomainAcceptable = offeringEnvironmentAcceptableCustomDomains
          .exists(_._2.domain == currentDomain)

        val currentUrlObject = new URI(currentUrl)
        val offeringEnvironmentUrlObject = new URI(offeringEnvironmentBaseUrlWithPrefixPath)
        val hasSameBaseUrlAndPrefix =
          currentUrlObject.getPath == offeringEnvironmentUrlObject.getPath &&
            currentUrlObject.getHost == offeringEnvironmentUrlObject.getHost

        val isNoLoginOffering = offeringId == OfferingId.DeprecatedNoLogin

        hasSameBaseUrlAndPrefix || isCurrentDomainAcceptable || isNoLoginOffering
      }
    } yield GetEnvironmentRedirectBaseUrlResponse(
      redirectBaseUrlWithPrefixPath = offeringEnvironmentBaseUrlWithPrefixPath,
      shouldNotRedirect = shouldNotRedirect
    )
  }

}
