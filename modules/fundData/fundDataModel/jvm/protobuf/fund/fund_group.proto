syntax = "proto3";

package anduin.protobuf.funddata.fund.group;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
    package_name: "anduin.protobuf.funddata.fund.group"
    single_file: true
    import: "anduin.id.funddata.FundDataFundGroupId"
    import: "anduin.model.common.user.UserId"
    import: "java.time.Instant"

};

message FundDataFundGroupModel {
    string fund_group_id = 1 [(scalapb.field).type = "FundDataFundGroupId"];
    string name = 2;
    bool is_deleted = 3;
    InstantMessage created_at = 4 [(scalapb.field).type = "Instant"];
    string created_by = 5 [(scalapb.field).type = "UserId"];
}

message RecordTypeUnion {
    FundDataFundGroupModel _FundDataFundGroupModel = 1;
}