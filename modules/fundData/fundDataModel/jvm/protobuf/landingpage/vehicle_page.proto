syntax = "proto3";

package anduin.protobuf.funddata.landingpage.page;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.funddata.landingpage.vehiclepage"
  single_file: true
  import: "java.time.Instant"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.webcontent.WebContentId"
  import: "anduin.id.funddata.FundDataFirmId"
};

message FundDataVehiclePageModel {
  reserved 8;
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  string page_name = 2;
  optional string custom_url = 3;
  InstantMessage created_at = 4 [(scalapb.field).type = "Instant"];
  string created_by = 5 [(scalapb.field).type = "UserId"];
  InstantMessage last_updated_at = 6 [(scalapb.field).type = "Instant"];
  string web_content_id = 7 [(scalapb.field).type = "WebContentId"];
  bool set_published = 10;
  bool is_deleted = 9;
}

message RecordTypeUnion {
  FundDataVehiclePageModel _FundDataVehiclePageModel = 1;
}