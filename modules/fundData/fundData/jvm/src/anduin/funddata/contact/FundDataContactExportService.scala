// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.funddata.contact

import java.time.Instant
import java.time.format.DateTimeFormatter

import org.apache.poi.ss.usermodel.IndexedColors
import org.apache.poi.xssf.usermodel.{XSSFFont, XSSFRichTextString}
import sttp.model.MediaType
import zio.{Task, ZIO}

import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.documentcontent.spreadsheet.FillSheetData.{FillSheetCell, FillSheetRow}
import anduin.documentcontent.spreadsheet.{FillSheetData, SpreadsheetUtils}
import anduin.fdb.record.{DefaultCluster, FDBRecordDatabase}
import anduin.funddata.communication.{FundDataFirmCommunicationService, FundDataFundLegalEntityCommunicationService}
import anduin.funddata.contact.note.FundDataContactNoteService
import anduin.funddata.endpoint.contact.*
import anduin.funddata.firm.FundDataFirmStoreOperations
import anduin.funddata.fund.fundlegalentity.FundLegalEntityOperations
import anduin.funddata.investmententity.InvestmentEntityStoreOperations
import anduin.funddata.investor.FundDataInvestorStoreOperations
import anduin.funddata.note.FundDataNoteUtils
import anduin.funddata.permission.FundDataPermissionService
import anduin.model.common.user.UserId
import anduin.storageservice.common.FileContentOrigin
import anduin.utils.DateTimeUtils

final case class FundDataContactExportService(
  fundDataPermissionService: FundDataPermissionService,
  fundDataContactService: FundDataContactService,
  fundDataFirmCommunicationService: FundDataFirmCommunicationService,
  fundDataFundLegalEntityCommunicationService: FundDataFundLegalEntityCommunicationService,
  fileService: FileService,
  fundDataContactNoteService: FundDataContactNoteService
) {

  private def generateSpreedSheetHeaderText(headerText: String, hasAsterisk: Boolean = false): XSSFRichTextString = {
    val headerFont = {
      val font = XSSFFont()
      font.setBold(true)
      font
    }

    val headerRichTextString = XSSFRichTextString()
    headerRichTextString.append(headerText, headerFont)

    if (hasAsterisk) {
      val asteriskFont = {
        val font = XSSFFont()
        font.setColor(IndexedColors.RED.getIndex)
        font
      }

      headerRichTextString.append(" ")
      headerRichTextString.append("*", asteriskFont)
    }

    headerRichTextString
  }

  def exportContacts(
    params: ExportContactsParams,
    actor: UserId
  ): Task[ExportContactsResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor export ${params.contactIds.size} contacts data")
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)

      contacts <- fundDataContactService
        .getFirmContactsInfo(
          params = GetFirmContactsInfoParams(
            firmId = params.firmId,
            filterByContacts = Some(params.contactIds)
          ),
          actor = actor
        )
        .map(_.data)

      refinedContacts <- ZIO.foreach(contacts) { contact =>
        for {
          contactRelations <- fundDataContactService.getContactRelation(
            params = GetContactRelationParams(
              firmId = params.firmId,
              contactId = contact.contactId
            ),
            actor = actor
          )

          contactMatrix <- fundDataContactService
            .getContactMatrix(
              params = GetContactMatrixParams(
                firmId = params.firmId,
                contactId = contact.contactId
              ),
              actor = actor
            )
            .map(_.contactMatrixValue)

        } yield (
          contact = contact,
          contactInvestmentEntityPermission = contactRelations.investmentEntityRelations,
          contactClientRelations = (contactRelations.contactRelationInfos.investors
            .map {
              _.investorId -> None
            } ++
            contactRelations.contactRelationInfos.investmentEntities.map { relation =>
              relation.investmentEntityId.parent -> Some(relation.investmentEntityId)
            })
            .groupMap(_._1)(_._2)
            .flatMap { case (investorId, investmentEntityIds) =>
              investmentEntityIds.distinct match {
                case None :: Nil =>
                  Seq(
                    (
                      clientId = investorId,
                      investmentEntityId = None
                    )
                  )
                case investmentEntityIds =>
                  investmentEntityIds.flatten.map { investmentEntityIdOpt =>
                    (
                      clientId = investorId,
                      investmentEntityId = Option(investmentEntityIdOpt)
                    )
                  }
              }

            }
            .toSeq,
          contactMatrix = contactMatrix
        )
      }

      clientNameMap <- FundDataInvestorStoreOperations
        .transact(
          _.getInvestors(refinedContacts.flatMap(_.contactClientRelations.map(_.clientId)).distinct)
        )
        .map(_.map { investor =>
          investor.investorId -> investor.name
        }.toMap)

      investmentEntityNameMap <- InvestmentEntityStoreOperations
        .transact(
          _.gets(refinedContacts.flatMap(_.contactClientRelations.flatMap(_.investmentEntityId)).distinct)
        )
        .map(_.map { investmentEntity =>
          investmentEntity.investmentEntityId -> investmentEntity.name
        }.toMap)

      fundLegalEntityNameMap <- FundLegalEntityOperations
        .transact(_.gets(refinedContacts.flatMap(_.contactMatrix.map(_.contactMatrixKey.fundLegalEntityId)).distinct))
        .map(_.map { fundLegalEntity =>
          fundLegalEntity.id -> fundLegalEntity.name
        }.toMap)

      contactsNoteMap <- fundDataContactNoteService.getContactsNote(
        firmId = params.firmId,
        contactIds = params.contactIds
      )

      contactInformationSheet = FillSheetData(
        sheetName = Some("1. Contact information"),
        startCol = 0,
        startRow = 0,
        rows = FillSheetRow(
          cells = List(
            FillSheetCell(generateSpreedSheetHeaderText("Email")),
            FillSheetCell(generateSpreedSheetHeaderText("First name")),
            FillSheetCell(generateSpreedSheetHeaderText("Last name")),
            FillSheetCell(generateSpreedSheetHeaderText("Custom ID")),
            FillSheetCell(generateSpreedSheetHeaderText("Company")),
            FillSheetCell(generateSpreedSheetHeaderText("Title")),
            FillSheetCell(generateSpreedSheetHeaderText("Phone")),
            FillSheetCell(generateSpreedSheetHeaderText("Number and street")),
            FillSheetCell(generateSpreedSheetHeaderText("City")),
            FillSheetCell(generateSpreedSheetHeaderText("State")),
            FillSheetCell(generateSpreedSheetHeaderText("Country")),
            FillSheetCell(generateSpreedSheetHeaderText("Zip code")),
            FillSheetCell(generateSpreedSheetHeaderText("Note"))
          )
        ) +: refinedContacts.map { case (contact, _, _, _) =>
          FillSheetRow(
            cells = List(
              FillSheetCell(contact.contactInfo.email),
              FillSheetCell(contact.contactInfo.firstName),
              FillSheetCell(contact.contactInfo.lastName),
              FillSheetCell(contact.contactInfo.customId),
              FillSheetCell(contact.contactInfo.company),
              FillSheetCell(contact.contactInfo.title),
              FillSheetCell(contact.contactInfo.phone),
              FillSheetCell(contact.contactInfo.numberAndStreet),
              FillSheetCell(contact.contactInfo.city),
              FillSheetCell(contact.contactInfo.state),
              FillSheetCell(contact.contactInfo.country),
              FillSheetCell(contact.contactInfo.zipCode),
              FillSheetCell(
                contactsNoteMap.getOrElse(contact.contactId, None).fold("") { note =>
                  FundDataNoteUtils.getTextFromNoteContent(note.content)
                }
              )
            )
          )
        }
      )

      contactRelationshipsSheet = FillSheetData(
        sheetName = Some("2. Contact Relationships"),
        startCol = 0,
        startRow = 0,
        rows = FillSheetRow(
          cells = List(
            FillSheetCell(generateSpreedSheetHeaderText("Email")),
            FillSheetCell(generateSpreedSheetHeaderText("Client")),
            FillSheetCell(generateSpreedSheetHeaderText("Investment Entity"))
          )
        ) +: refinedContacts.flatMap { case (contact, _, contactClientRelations, _) =>
          contactClientRelations.map { relation =>
            FillSheetRow(
              cells = List(
                FillSheetCell(contact.contactInfo.email),
                FillSheetCell(clientNameMap.getOrElse(relation.clientId, "")),
                FillSheetCell(relation.investmentEntityId.flatMap(investmentEntityNameMap.get).getOrElse(""))
              )
            )
          }
        }
      )

      ieLevelCommunicationSettingSheet = FillSheetData(
        sheetName = Some("3.1. IE-Level Communication Settings"),
        startCol = 0,
        startRow = 0,
        rows = FillSheetRow(
          cells = List(
            FillSheetCell(generateSpreedSheetHeaderText("Email")),
            FillSheetCell(generateSpreedSheetHeaderText("Investment Entity")),
            FillSheetCell(generateSpreedSheetHeaderText("Invitation emails")),
            FillSheetCell(generateSpreedSheetHeaderText("Document requests"))
          )
        ) +: refinedContacts.flatMap { case (contact, contactInvestmentEntityPermission, _, _) =>
          contactInvestmentEntityPermission.map { permission =>
            FillSheetRow(
              cells = List(
                FillSheetCell(contact.contactInfo.email),
                FillSheetCell(investmentEntityNameMap.getOrElse(permission.investmentEntityId, "")),
                FillSheetCell(permission.fundSubCommunicationType match {
                  case FundDataContact.FundSubCommunicationType.MainInvestor => "Yes"
                  case FundDataContact.FundSubCommunicationType.Empty        => "No"
                }),
                FillSheetCell(permission.documentRequestCommunicationType match {
                  case FundDataContact.DocumentRequestCommunicationType.NonReceiver => "No"
                  case FundDataContact.DocumentRequestCommunicationType.Receiver    => "Yes"
                })
              )
            )
          }
        }
      )

      firmCommunicationTypes <- fundDataFirmCommunicationService
        .getFirmCommunicationsUnsafe(params.firmId)
        .map(_.sortBy(_.name))

      fleCommunicationTypeMap <- ZIO
        .foreach(
          refinedContacts.flatMap(_.contactMatrix.map(_.contactMatrixKey.fundLegalEntityId)).distinct
        ) { fundLegalEntityId =>
          fundDataFundLegalEntityCommunicationService
            .getFundLegalEntityCommunicationsUnsafe(fundLegalEntityId)
            .map(fundLegalEntityId -> _.map(_.communicationTypeId).toSet)
        }
        .map(_.toMap)

      fundCommunicationMatricesSheet = FillSheetData(
        sheetName = Some("3.2. Fund Communication Matrices"),
        startCol = 0,
        startRow = 0,
        rows = FillSheetRow(
          cells = List(
            FillSheetCell(generateSpreedSheetHeaderText("Email")),
            FillSheetCell(generateSpreedSheetHeaderText("Investment Entity")),
            FillSheetCell(generateSpreedSheetHeaderText("Fund Legal Entity"))
          ) ++ firmCommunicationTypes.map { communicationType =>
            FillSheetCell(generateSpreedSheetHeaderText(communicationType.name))
          }
        ) +: refinedContacts.flatMap { case (contact, _, _, contactMatrices) =>
          contactMatrices
            .groupMap { matrix =>
              (
                investmentEntityId = matrix.contactMatrixKey.investmentEntityId,
                fundLegalEntityId = matrix.contactMatrixKey.fundLegalEntityId
              )
            } { matrix =>
              matrix.contactMatrixKey.communicationTypeId -> matrix.communicationRole
            }
            .view
            .mapValues(_.toMap)
            .toSeq
            .map { case ((investmentEntityId, fundLegalEntityId), communicationRoleMap) =>
              val fleCommunicationTypeIds = fleCommunicationTypeMap.getOrElse(fundLegalEntityId, Set.empty)
              FillSheetRow(
                cells = List(
                  FillSheetCell(contact.contactInfo.email),
                  FillSheetCell(investmentEntityNameMap.getOrElse(investmentEntityId, "")),
                  FillSheetCell(fundLegalEntityNameMap.getOrElse(fundLegalEntityId, ""))
                ) ++ firmCommunicationTypes.map { communicationType =>
                  FillSheetCell(
                    if (fleCommunicationTypeIds.contains(communicationType.communicationTypeId)) {
                      communicationRoleMap
                        .get(communicationType.communicationTypeId)
                        .fold(
                          "Unassigned"
                        ) {
                          case FundDataContact.CommunicationRole.Primary   => "Primary"
                          case FundDataContact.CommunicationRole.Secondary => "Secondary"
                        }
                    } else {
                      "N/A"
                    }
                  )
                }
              )
            }
        }
      )

      workbookStream <- SpreadsheetUtils.createAndFillSpreadsheet(
        Seq(
          contactInformationSheet,
          contactRelationshipsSheet,
          ieLevelCommunicationSettingSheet,
          fundCommunicationMatricesSheet
        )
      )

      firmName <- FDBRecordDatabase.transact(FundDataFirmStoreOperations.Production)(_.get(params.firmId)).map(_.name)

      workbookName =
        s"$firmName Contact Data Export ${DateTimeUtils.formatInstant(Instant.now(), DateTimeFormatter.ofPattern("yyyyMMdd"))(
            using DateTimeUtils.defaultTimezone
          )}.xlsx"

      folderId <- fileService.createUserTemporaryFolderIfNeeded(actor)

      generatedFileId <- fileService.uploadFile(
        parentFolderId = folderId,
        fileName = workbookName,
        content = FileContentOrigin.FromSource(
          workbookStream,
          MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        ),
        uploader = actor
      )

    } yield ExportContactsResponse(generatedFileId)
  }

}
