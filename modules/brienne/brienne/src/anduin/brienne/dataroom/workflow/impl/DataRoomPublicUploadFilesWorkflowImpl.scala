// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.brienne.dataroom.workflow.impl

import anduin.brienne.dataroom.workflow.activity.DataRoomUploadFilesActivity
import anduin.brienne.dataroom.workflow.{
  DataRoomPublicUploadFilesWorkflow,
  DataRoomUploadFilesWorkflowInput,
  DataRoomUploadFilesWorkflowOutput
}
import anduin.brienne.workflow.AsyncPublicApiWorkflow
import anduin.brienne.workflow.core.RequestError
import anduin.brienne.workflow.request.{RequestParams, RequestResponse}
import anduin.service.PublicApiContext
import anduin.workflow.TemporalWorkflowService.newActivityStub
import anduin.workflow.WorkflowTask
import anduin.workflow.common.TemporalWorkflowImplCompanion

class DataRoomPublicUploadFilesWorkflowImpl
    extends DataRoomPublicUploadFilesWorkflow
    with AsyncPublicApiWorkflow.Impl[
      DataRoomUploadFilesWorkflowInput,
      DataRoomUploadFilesWorkflowOutput
    ] {

  private val uploadFilesActivity = newActivityStub[DataRoomUploadFilesActivity]

  override protected def task(
    ctx: PublicApiContext,
    params: DataRoomUploadFilesWorkflowInput
  ): WorkflowTask[Either[RequestError, DataRoomUploadFilesWorkflowOutput]] = {
    WorkflowTask.executeActivity(uploadFilesActivity.uploadFiles(params, ctx))
  }

}

object DataRoomPublicUploadFilesWorkflowImpl
    extends TemporalWorkflowImplCompanion[
      DataRoomPublicUploadFilesWorkflow,
      DataRoomPublicUploadFilesWorkflowImpl
    ]
