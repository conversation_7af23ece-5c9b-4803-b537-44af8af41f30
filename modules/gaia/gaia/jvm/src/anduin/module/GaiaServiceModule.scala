// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.module

import com.softwaremill.macwire.wire

import anduin.annotation.service.*
import anduin.blueprint.service.{BlueprintFileService, BlueprintLockService, BlueprintStorageService, CatalaService}
import anduin.cue.service.{CueModuleService, CueTableService}
import anduin.digitization.service.*
import anduin.forms.service.*
import anduin.forms.service.textract.FormTextractService
import anduin.ontology.service.{
  CommonAsaMappingService,
  DocumentAsaMappingService,
  DocumentSaProfileMappingService,
  FormAsaMappingService,
  FormSaProfileMappingService,
  MappingAuthorizationService
}
import anduin.sa.service.SaCueService
import anduin.signature.integration.SignatureIntegrationService

trait GaiaServiceModule extends GondorCoreServiceModule with OntologyServiceModule with SaServiceModule {

  def signatureIntegrationService: SignatureIntegrationService

  // Keep at least 1 unwired service to combat zinc bug
  given catalaService: CatalaService = CatalaService()

  given blueprintService: BlueprintFileService = wire[BlueprintFileService]
  given blueprintStorageService: BlueprintStorageService = wire[BlueprintStorageService]
  given blueprintLockService: BlueprintLockService = wire[BlueprintLockService]

  // New form
  given formStorageService: FormStorageService = wire[FormStorageService]
  given formService: FormService = wire[FormService]
  given dataLayerService: DataLayerService = wire[DataLayerService]
  given formIntegrationService: FormIntegrationService = wire[FormIntegrationService]
  given formTestScriptService: FormTestScriptService = wire[FormTestScriptService]
  given assetLockService: AssetLockService = wire[AssetLockService]
  given formLockService: FormLockService = wire[FormLockService]
  given formDataService: FormDataService = wire[FormDataService]
  given pdfAnnotationService: PdfAnnotationService = wire[PdfAnnotationService]
  given dataTemplateService: DataTemplateService = wire[DataTemplateService]
  given formMappingService: FormMappingService = wire[FormMappingService]
  given formMatchingService: FormMatchingService = wire[FormMatchingService]
  given formFolderService: FormFolderService = wire[FormFolderService]
  given formToolConfigService: FormToolConfigService = wire[FormToolConfigService]
  given annotationDocumentService: AnnotationDocumentService = wire[AnnotationDocumentService]

  given commonAsaMappingService: CommonAsaMappingService = wire[CommonAsaMappingService]
  given formAsaMappingService: FormAsaMappingService = wire[FormAsaMappingService]
  given documentAsaMappingService: DocumentAsaMappingService = wire[DocumentAsaMappingService]

  given mappingAuthorizationService: MappingAuthorizationService = wire[MappingAuthorizationService]
  given documentSaProfileMappingService: DocumentSaProfileMappingService = wire[DocumentSaProfileMappingService]
  given formSaProfileMappingService: FormSaProfileMappingService = wire[FormSaProfileMappingService]
  given saCueService: SaCueService = wire[SaCueService]

  given annotationDocumentStorageService: AnnotationDocumentStorageService =
    wire[AnnotationDocumentStorageService]

  given textractAnnotationTargetStorageService: TextractAnnotationTargetStorageService =
    wire[TextractAnnotationTargetStorageService]

  given textractAnnotationStorageService: TextractAnnotationStorageService =
    wire[TextractAnnotationStorageService]

  given annotationDocumentLockService: AnnotationDocumentLockService = wire[AnnotationDocumentLockService]
  given formTextractService: FormTextractService = wire[FormTextractService]

  given formDataUserTempService: FormDataUserTempService = wire[FormDataUserTempService]

  // Form template mapping
  given formTemplateMappingService: FormTemplateMappingService = wire[FormTemplateMappingService]
  given formTemplateMappingLockService: FormTemplateMappingLockService = wire[FormTemplateMappingLockService]

  given formTestToolsService: FormTestToolsService = wire[FormTestToolsService]

  // Digitization Platform
  given digitizationFolderService: DigitizationFolderService = wire[DigitizationFolderService]
  given digitizationFileService: DigitizationFileService = wire[DigitizationFileService]
  given digitizationTagService: DigitizationTagService = wire[DigitizationTagService]
  given digitizationSearchService: DigitizationSearchService = wire[DigitizationSearchService]
  given digitizationPermissionService: DigitizationPermissionService = wire[DigitizationPermissionService]
  given digitizationImportExportService: DigitizationImportExportService = wire[DigitizationImportExportService]

  // Cue module
  given cueModuleService: CueModuleService = wire[CueModuleService]
  given cueTableService: CueTableService = wire[CueTableService]

}
