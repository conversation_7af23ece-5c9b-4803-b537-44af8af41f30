// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.storage

import com.apple.foundationdb.record.RecordMetaDataBuilder
import com.apple.foundationdb.record.metadata.Key.Expressions

import anduin.fdb.record.model.FDBTupleConverter
import anduin.fdb.record.model.common.RadixIdTupleConverter
import anduin.fdb.record.{FDBRecordEnum, FDBRecordKeySpace, FDBRecordStoreProvider, FDBStoreProviderCompanion}
import anduin.forms.model.testscript.content.{FormTestScriptContent, FormTestScriptContentProto}
import anduin.id.form.FormTestScriptId

final case class FormTestScriptContentStoreProvider(
  override protected val keySpace: FDBRecordKeySpace
) extends FDBRecordStoreProvider[FDBRecordEnum.FormTestScriptContent.type](
      FDBRecordEnum.FormTestScriptContent,
      FormTestScriptContentProto
    ) {

  override protected def recordBuilderFn(builder: RecordMetaDataBuilder): Unit = {
    builder
      .getRecordType(FormTestScriptContentStoreProvider.RecordTypeName)
      .setPrimaryKey(FormTestScriptContentStoreProvider.primaryKeyExpression)
  }

  override protected def indexes: Seq[IndexMappingWithVersion] = Seq()

}

object FormTestScriptContentStoreProvider extends FDBStoreProviderCompanion[FDBRecordEnum.FormTestScriptContent.type] {

  private val RecordTypeName: String = FormTestScriptContent.scalaDescriptor.name

  private val primaryKeyExpression = Expressions.field("id")

  given primaryKeyTupleConverter: FDBTupleConverter[FormTestScriptId] =
    RadixIdTupleConverter.instance[FormTestScriptId]

  given modelMapping: Mapping[FormTestScriptId, FormTestScriptContent] = mappingInstance
}
