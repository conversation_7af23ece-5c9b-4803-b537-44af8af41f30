// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.ria.client.fetch

import com.raquo.laminar.api.L.*
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.id.ria.RiaEntityId
import anduin.ria.client.fetch.JoinRiaEntity.{handleUserJoinEntity, isJoinedVar, isJoiningSignal, joinEventBus}
import anduin.ria.endpoints.JoinRiaEntityParams
import anduin.ria.RiaClient

final case class JoinRiaEntity(
  riaEntityId: RiaEntityId,
  renderer: Signal[Boolean] => HtmlElement
) {

  def apply(): Node = {
    div(
      joinEventBus.events.flatMapSwitch(_ => handleUserJoinEntity(riaEntityId)) --> Observer.empty,
      onMountCallback { _ =>
        if (!isJoinedVar.now()) {
          joinEventBus.emit(())
        }
        ()
      },
      renderer(isJoiningSignal)
    )
  }

}

object JoinRiaEntity {
  private val isJoiningVar: Var[Boolean] = Var(false)
  private val isJoiningSignal: Signal[Boolean] = isJoiningVar.signal

  private val isJoinedVar: Var[Boolean] = Var(false)
  private val joinEventBus = new EventBus[Unit]

  private def handleUserJoinEntity(riaEntityId: RiaEntityId): EventStream[Unit] = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isJoiningVar.set(true))
        _ <- RiaClient
          .joinRiaEntity(JoinRiaEntityParams(riaEntityId))
          .map(
            _.fold(
              _ => (),
              _ => isJoinedVar.set(true)
            )
          )
      } yield isJoiningVar.set(false)
    )
  }

}
