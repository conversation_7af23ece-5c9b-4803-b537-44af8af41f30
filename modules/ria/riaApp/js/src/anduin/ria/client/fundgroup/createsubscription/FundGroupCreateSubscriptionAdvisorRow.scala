// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.ria.client.fundgroup.createsubscription

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.style.tw.*

import anduin.ria.client.fundgroup.createsubscription.FundGroupCreateSubscriptionButton.AdvisorSubscriptionData
import anduin.scalajs.pluralize.Pluralize

final case class FundGroupCreateSubscriptionAdvisorRow(
  advisorSubscriptionDataSignal: Signal[AdvisorSubscriptionData],
  onAddInvestmentEntity: Observer[Unit],
  onChangeInvestmentEntity: Observer[(Int, String)],
  onRemoveInvestmentEntity: Observer[Int],
  isDisabledSignal: Signal[Boolean]
) {

  private val hasEmptyInvestmentEntityNameSignal =
    advisorSubscriptionDataSignal.map(_.investmentEntityNames.exists(_.isEmpty))

  private val advisorSignal = advisorSubscriptionDataSignal.map(_.advisor)
  private val investmentEntityNamesSignal = advisorSubscriptionDataSignal.map(_.investmentEntityNames)

  def apply(): HtmlElement = {
    div(
      tw.bgGray1.borderAll.rounded4.p12,
      hasEmptyInvestmentEntityNameSignal.cls(tw.borderWarning4, tw.borderGray3),
      div(
        tw.grid.gridCols2,
        div(
          child <-- advisorSignal.map { advisor =>
            div(
              tw.flex.itemsCenter.gapX4.pt6,
              div(
                tw.fontSemiBold,
                advisor.info.userInfo.fullNameString
              ),
              span("·"),
              div(
                tw.textGray7,
                advisor.info.userInfo.emailAddressStr
              )
            )
          }
        ),
        div(
          tw.spaceY8,
          children <-- advisorSubscriptionDataSignal
            .map(_.investmentEntityNames)
            .splitByIndex { (index, _, investmentEntityNameSignal) =>
              FundGroupCreateSubscriptionInvestmentEntityRow(
                investmentEntityNameSignal = investmentEntityNameSignal,
                onChangeInvestmentEntityName = onChangeInvestmentEntity.contramap((index, _)),
                onRemoveSubscriptionData = onRemoveInvestmentEntity.contramap(_ => index),
                isDisabledSignal = isDisabledSignal
              )()
            },
          child.maybe <-- hasEmptyInvestmentEntityNameSignal.map {
            Option.when(_) {
              div(
                tw.textWarning5.text11,
                "If left blank, the advisor’s name will be used as the investment entity"
              )
            }
          },
          div(
            ButtonL(
              style = ButtonL.Style.Text(
                color = ButtonL.Color.Primary,
                icon = Some(Icon.Glyph.Plus)
              ),
              onClick = onAddInvestmentEntity.contramap(_ => ())
            )("Add additional entity")
          )
        )
      ),
      DividerL()(),
      div(
        tw.textRight,
        text <-- investmentEntityNamesSignal
          .map(_.length)
          .combineWith(advisorSignal)
          .map { (count, advisor) =>
            s"${Pluralize("subscription", count, true)} will be created for ${advisor.info.userInfo.fullNameString}"
          }
      )
    )
  }

}
