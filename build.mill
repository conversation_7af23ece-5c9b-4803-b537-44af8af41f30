//| mvnDeps:
//| - com.lihaoyi::mill-contrib-buildinfo:$MILL_VERSION
//| - com.lihaoyi::mill-contrib-artifactory:$MILL_VERSION
//| - com.lihaoyi::mill-contrib-jmh:$MILL_VERSION
//| - io.github.hoangmaihuy::mill-missinglink::0.3.0-RC1
//| - io.github.hoangmaihuy::mill-universal-packager::0.2.0-RC1
//| - com.anduin::osiris::0.1.1-384-0820c0

// Build imports
import build.platform.stargazerBuildInfo
import build.modules
import build.gondor
import build.itools
import build.js
import build.apps

// Scala imports
import mill.*
import mill.main.MainRootModule
import anduin.build.*
import anduin.mill.utils.*

object `package` extends VisualizeModuleDeps {

  def workspacePackageJson = Task {
    mill.define.BuildCtx.withFilesystemCheckerDisabled {
      // Write child projects package.json
      gondor.gondorWebResources.packageJson()
      itools.pantheon.pantheonWebWorker.packageJson()
      itools.pantheon.pantheon.js.packageJson()
      itools.olympian.js.packageJson()
      js.anduinBootstrap.packageJson()
      modules.dataextract.dataextractApp.js.packageJson()
      modules.heimdall.heimdallApp.js.packageJson()
      modules.signature.signatureApp.js.packageJson()
      modules.investorProfile.investorProfileApp.js.packageJson()
      modules.dataroom.dataroomApp.js.packageJson()
      modules.fundData.fundDataApp.js.packageJson()
      modules.fundsub.fundsubApp.js.packageJson()
      modules.integplatform.integplatformApp.js.packageJson()
      modules.narya.js.packageJson()
      modules.ria.riaApp.js.packageJson()
      apps.maya.mayaApp.js.packageJson()
      apps.gondor.gondorAppClient.packageJson()
      // Root package json
      val pkgJson = ujson.Obj(
        "license" -> "UNLICENSED",
        "name" -> "stargazer",
        "private" -> true,
        "browser" -> ujson.Obj(
          "util" -> false
        ),
        "workspaces" -> ujson.Arr(
          s"${gondor.gondorWebResources.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${itools.pantheon.pantheonWebWorker.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${itools.pantheon.pantheon.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${itools.olympian.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${js.anduinBootstrap.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${modules.dataextract.dataextractApp.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${modules.heimdall.heimdallApp.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${modules.signature.signatureApp.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${modules.investorProfile.investorProfileApp.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${modules.dataroom.dataroomApp.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${modules.fundData.fundDataApp.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${modules.fundsub.fundsubApp.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${modules.integplatform.integplatformApp.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${modules.narya.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${modules.ria.riaApp.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${apps.maya.mayaApp.js.jsSpecsDir().subRelativeTo(Task.workspace)}/",
          s"${apps.gondor.gondorAppClient.jsSpecsDir().subRelativeTo(Task.workspace)}/"
        ),
        "packageManager" -> s"yarn@${AnduinVersions.npmDev.yarnVersion}"
      )
      val path = moduleDir / "package.json"
      os.write.over(path, pkgJson.render(2) + "\n")
      PathRef(path)
    }
  }

  def workspaceJsPackageInstall = Task {
    mill.define.BuildCtx.withFilesystemCheckerDisabled {

      val allowYarnUpdate = Task.ctx().env.get("ALLOW_YARN_UPDATE").contains("true")

      workspacePackageJson()

      val installCommand = if (allowYarnUpdate) {
        Seq("yarn", "install")
      } else {
        Seq("yarn", "install", "--immutable")
      }

      Task.log.info(s"Running workspace package install with yarn: ${installCommand}")

      try {
        os.proc(installCommand)
          .call(
            cwd = Task.workspace,
            env = Map.empty,
            stdout = os.Inherit,
            stderr = os.Inherit
          )
      } catch {
        case e: Exception =>
          throw new RuntimeException("Error running yarn install", e)
      }

      PathRef(Task.workspace / "node_modules")
    }
  }

  // Command aliases
  def devBuildAllClients = Task {
    Task.log.info("Building all client apps in dev mode")
    devBuildWebResources()
    devBuildDataExtractClient()
    devBuildHeimdallClient()
    devBuildSignatureClient()
    devBuildInvestorProfileClient()
    devBuildDataroomClient()
    devBuildFundDataClient()
    devBuildPantheonClient()
    devBuildItoolsClient()
    devBuildNaryaClient()
    devBuildFundsubClient()
    devBuildMayaClient()
    devBuildGondorClient()
    devBuildRiaClient()
  }

  def devBuildAllClientsExceptFundsub = Task {
    Task.log.info("Building all client apps in dev mode except Fundsub")
    devBuildWebResources()
    devBuildDataExtractClient()
    devBuildHeimdallClient()
    devBuildSignatureClient()
    devBuildInvestorProfileClient()
    devBuildDataroomClient()
    devBuildFundDataClient()
    devBuildIntegPlatformClient()
    devBuildPantheonClient()
    devBuildItoolsClient()
    devBuildNaryaClient()
    devBuildMayaClient()
    devBuildGondorClient()
    devBuildRiaClient()
  }

  def devBuildWebResources = Task {
    js.anduinBootstrap.fastBuildClient()
    stargazerBuildInfo.js.jsBuildInfo()
    gondor.gondorWebResources.fastBuildClient()
  }

  def devBuildDataExtractClient = modules.dataextract.dataextractApp.js.fastBuildClient
  def devBuildHeimdallClient = modules.heimdall.heimdallApp.js.fastBuildClient
  def devBuildSignatureClient = modules.signature.signatureApp.js.fastBuildClient
  def devBuildInvestorProfileClient = modules.investorProfile.investorProfileApp.js.fastBuildClient
  def devBuildDataroomClient = modules.dataroom.dataroomApp.js.fastBuildClient
  def devBuildFundDataClient = modules.fundData.fundDataApp.js.fastBuildClient
  def devBuildIntegPlatformClient = modules.integplatform.integplatformApp.js.fastBuildClient
  def devBuildRiaClient = modules.ria.riaApp.js.fastBuildClient

  def devBuildPantheonClient = Task {
    itools.pantheon.pantheonWebWorker.fastBuildClient()
    itools.pantheon.pantheon.js.fastBuildClient()
  }

  def devBuildItoolsClient = itools.olympian.js.fastBuildClient
  def devBuildNaryaClient = modules.narya.js.fastBuildClient
  def devBuildFundsubClient = modules.fundsub.fundsubApp.js.fastBuildClient
  def devBuildMayaClient = apps.maya.mayaApp.js.fastBuildClient
  def devBuildGondorClient = apps.gondor.gondorAppClient.fastBuildClient

  def prodBuildAllClients = Task {
    Task.log.info("Building all client apps in production mode")
    prodBuildWebResources()
    prodBuildDataExtractClient()
    prodBuildHeimdallClient()
    prodBuildSignatureClient()
    prodBuildInvestorProfileClient()
    prodBuildDataroomClient()
    prodBuildFundDataClient()
    prodBuildIntegPlatformClient()
    prodBuildPantheonClient()
    prodBuildItoolsClient()
    prodBuildNaryaClient()
    prodBuildFundsubClient()
    prodBuildMayaClient()
    prodBuildGondorClient()
    prodBuildRiaClient()
  }

  def prodBuildWebResources = Task {
    js.anduinBootstrap.fullBuildClient()
    stargazerBuildInfo.js.jsBuildInfo()
    gondor.gondorWebResources.fullBuildClient()
  }

  def prodBuildDataExtractClient = modules.dataextract.dataextractApp.js.fullBuildClient

  def prodBuildHeimdallClient = modules.heimdall.heimdallApp.js.fullBuildClient

  def prodBuildSignatureClient = modules.signature.signatureApp.js.fullBuildClient

  def prodBuildInvestorProfileClient = modules.investorProfile.investorProfileApp.js.fullBuildClient

  def prodBuildDataroomClient = modules.dataroom.dataroomApp.js.fullBuildClient

  def prodBuildFundDataClient = modules.fundData.fundDataApp.js.fullBuildClient

  def prodBuildIntegPlatformClient = modules.integplatform.integplatformApp.js.fullBuildClient

  def prodBuildRiaClient = modules.ria.riaApp.js.fullBuildClient

  def prodBuildPantheonClient = Task {
    itools.pantheon.pantheonWebWorker.fullBuildClient()
    itools.pantheon.pantheon.js.fullBuildClient()
  }

  def prodBuildItoolsClient = itools.olympian.js.fullBuildClient

  def prodBuildNaryaClient = modules.narya.js.fullBuildClient

  def prodBuildFundsubClient = modules.fundsub.fundsubApp.js.fullBuildClient

  def prodBuildMayaClient = apps.maya.mayaApp.js.fullBuildClient

  def prodBuildGondorClient = apps.gondor.gondorAppClient.fullBuildClient

  def startDevServer(args: Task[Args] = Task.Anon(Args())): Command[Unit] = apps.gondor.gondorAppServer.run(args)

  def runMigration(args: String*): Command[Unit] =
    apps.gondor.gondorAppServer.runMain("com.anduin.stargazer.apps.stargazer.GondorMigrationApp", args*)

  def runHotfixMigration(args: String*): Command[Unit] =
    apps.gondor.gondorAppServer.runMain("com.anduin.stargazer.apps.stargazer.GondorHotfixMigrationApp", args*)

  def runEndpointReport(args: String*): Command[Unit] =
    apps.gondor.gondorAppServer.runMain("com.anduin.stargazer.apps.stargazer.EndpointReportApp", args*)

  def initTestData(args: String*) =
    apps.gondor.gondorAppServer.runMain("com.anduin.stargazer.apps.stargazer.GondorTestInitializer", args*)

  def reStart(): Command[Unit] = apps.gondor.gondorAppServer.runBackground()

  def reStop(): Command[Unit] = Task.Command {
    val processIdFile =
      apps.gondor.gondorAppServer.dir() / os.up / "runBackground.dest" / "newest-pid"
    if (os.exists(processIdFile)) {
      os.remove(processIdFile)
    }
    ()
  }

}
